/*************************************************************
  Optimized AC Control System with Dual IR Distribution

  Blynk IoT Platform Integration for ESP8266 NodeMCU
  - Dual IR LED setup for power load balancing
  - Optimized auto AC/fan functionality
  - Enhanced temperature control logic
  - Improved Blynk communication patterns

  Hardware Setup:
  - NodeMCU ESP8266
  - DHT22 Temperature/Humidity Sensor on D6
  - IR LED 1 on D2 (GPIO 4) - Primary AC/Speaker control
  - IR LED 2 on D1 (GPIO 5) - Secondary AC/Speaker control
  - Both IR LEDs work in parallel for better power distribution

  Author: Optimized for Blynk IoT Platform
  Version: 3.0 - Dual IR & Performance Optimized
 *************************************************************/

#define BLYNK_TEMPLATE_ID "TMPLn1fK4fPE"
#define BLYNK_TEMPLATE_NAME "AC CONTROL"
#define BLYNK_FIRMWARE_VERSION "3.0"

// Blynk Configuration - Optimized for IoT Platform
#define BLYNK_PRINT Serial
#define APP_DEBUG
// Reduce message frequency to optimize data usage
#define BLYNK_MSG_LIMIT 20

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include "DHT.h"
#define DHTTYPE DHT22
#define DHTPIN D6

// Board Configuration
#define USE_NODE_MCU_BOARD
#include "BlynkEdgent.h"

// Dual IR LED Configuration for Power Load Balancing
const uint16_t kIrLed1 = D2;  // GPIO 4 - Primary IR LED
const uint16_t kIrLed2 = D1;  // GPIO 5 - Secondary IR LED
IRsend irsend1(kIrLed1);      // Primary IR sender
IRsend irsend2(kIrLed2);      // Secondary IR sender

// Sensor and Timer Objects
DHT dht(DHTPIN, DHTTYPE);
BlynkTimer timer;

// System State Variables - Optimized naming
struct SystemState {
  bool autoMode = false;
  bool fanMode = false;
  float coldThreshold = 24.0;
  float hotThreshold = 25.0;
  int targetTemp = 18;
  float exactTemp = 0.0;

  // Auto control state tracking
  int autoStage = 0;        // Replaces 'a'
  int stabilityCounter = 0; // Replaces 'b'
  bool tempHandled = false; // Replaces 'thand'
  bool initialized = false; // Replaces 'c' and 'd'
} sysState;

// Performance optimization - reduce DHT readings
unsigned long lastDHTRead = 0;
const unsigned long DHT_READ_INTERVAL = 2000; // Read every 2 seconds
float lastTemp = 0.0;
float lastHumidity = 0.0;


// ============================================================================
// OPTIMIZED SENSOR FUNCTIONS
// ============================================================================

// Optimized DHT reading with caching to reduce sensor stress
bool readDHTSensor() {
  unsigned long currentTime = millis();

  // Only read sensor if enough time has passed
  if (currentTime - lastDHTRead >= DHT_READ_INTERVAL) {
    float temp = dht.readTemperature();
    float humidity = dht.readHumidity();

    if (!isnan(temp) && !isnan(humidity)) {
      lastTemp = temp;
      lastHumidity = humidity;
      lastDHTRead = currentTime;
      return true;
    } else {
      Serial.println("DHT22 Read Error - Using cached values");
      return false;
    }
  }
  return true; // Use cached values
}

// Optimized sensor data transmission to Blynk
void SendSensor() {
  // Initialize system on first run
  if (!sysState.initialized) {
    Blynk.syncVirtual(V7);
    Blynk.syncVirtual(V8);
    Blynk.syncVirtual(V9);
    AUTOTEMP();
    sysState.initialized = true;
  }

  // Read sensor data
  if (!readDHTSensor()) {
    return; // Skip this cycle if sensor read failed
  }

  // Optimized status message based on system state
  String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "%";

  if (sysState.autoMode) {
    statusMsg += " | AUTO AC: ON";
    if (sysState.fanMode) statusMsg += " (FAN MODE)";
    statusMsg += " | Range: " + String(sysState.coldThreshold, 1) + "-" + String(sysState.hotThreshold, 1) + "°C";
  } else {
    statusMsg += " | AUTO AC: OFF";
  }

  // Send to Blynk with reduced frequency
  Blynk.virtualWrite(V6, statusMsg);

  // Debug output
  Serial.println("Sensor Data: " + statusMsg);
}

// ============================================================================
// DUAL IR TRANSMISSION FUNCTIONS
// ============================================================================

// Send IR command through both IR LEDs for power distribution and reliability
void sendDualIR_NEC(uint32_t data, uint16_t nbits) {
  Serial.println("Sending NEC via dual IR LEDs: 0x" + String(data, HEX));

  // Send through primary IR LED
  irsend1.sendNEC(data, nbits);
  delay(50); // Small delay between transmissions

  // Send through secondary IR LED for redundancy
  irsend2.sendNEC(data, nbits);

  Serial.println("Dual IR NEC transmission completed");
}

// Send raw IR data through both IR LEDs
void sendDualIR_Raw(uint16_t rawData[], uint16_t length, uint16_t frequency) {
  Serial.println("Sending RAW IR via dual LEDs, Length: " + String(length));

  // Send through primary IR LED
  irsend1.sendRaw(rawData, length, frequency);
  delay(100); // Longer delay for raw data

  // Send through secondary IR LED
  irsend2.sendRaw(rawData, length, frequency);

  Serial.println("Dual IR RAW transmission completed");
}
// ============================================================================
// OPTIMIZED SPEAKER CONTROL FUNCTIONS
// ============================================================================

BLYNK_WRITE(V3)  // Speaker Control with Dual IR
{
  readDHTSensor(); // Ensure we have current sensor data
  int command = param.asInt();

  switch(command) {
    case 0: // Speaker Off
      Serial.println("Speaker: OFF");
      sendDualIR_NEC(0x807F48B7, 32);
      break;

    case 1: // Speaker On with Bluetooth
      Serial.println("Speaker: ON (Bluetooth)");
      sendDualIR_NEC(0x807F48B7, 32);
      break;

    case 2: // Speaker Bluetooth Mode
      Serial.println("Speaker: Bluetooth Mode");
      sendDualIR_NEC(0x807FD827, 32);
      delay(100);
      sendDualIR_NEC(0x807FD827, 32); // Send twice for reliability
      break;

    case 3: // Display Toggle
      {
        uint16_t displayToggleData[243] = {
          8308, 4264, 464, 606, 466, 1660, 490, 1686, 466, 584, 488, 1630, 518, 580, 492, 1658, 492, 582, 490, 578, 492, 1660, 490, 578, 494, 606, 464, 1660, 490, 1684, 466, 1658, 492, 582, 492, 580, 492, 554, 518, 580, 492, 606, 464, 582, 490, 580, 492, 580, 490, 608, 464, 606, 464, 606, 466, 578, 492, 1658, 490, 584, 488, 580, 490, 584, 490, 606, 464, 1658, 490, 580, 492, 580, 490, 606, 464, 580, 492, 1684, 464, 580, 492, 580, 492, 582, 488, 554, 516, 582, 490, 582, 490, 580, 490, 580, 492, 580, 492, 582, 490, 580, 492, 582, 490, 582, 490, 608, 464, 580, 492, 580, 492, 580, 490, 580, 492, 582, 490, 580, 490, 582, 490, 606, 464, 606, 464, 580, 492, 580, 492, 580, 492, 580, 492, 580, 492, 1686, 464, 1656, 492, 584, 490, 578, 492, 582, 488, 582, 490, 580, 492, 580, 492, 580, 492, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 1656, 492, 582, 490, 1656, 494, 578, 494, 580, 492, 582, 490, 582, 490, 582, 492, 1658, 492, 1658, 494, 578, 494, 578, 492, 1660, 492, 1658, 492, 580, 492, 582, 490, 580, 492, 1658, 492, 1658, 492, 582, 490, 1658, 492, 1656, 492, 580, 492, 580, 492, 578, 494, 580, 492, 1658, 492, 1660, 492, 580, 492, 578, 494, 580, 492, 578, 494, 1656, 492, 580, 492, 1658, 492, 578, 494, 1656, 494, 580, 494, 1654, 494, 578, 492
        };
        Serial.println("Display: Toggle ON/OFF");
        sendDualIR_Raw(displayToggleData, 243, 38);
      }
      break;

    case 4: // AC Cool Mode
      {
        uint16_t coolModeData[243] = {
          8298, 4242, 490, 584, 516, 1634, 518, 1658, 464, 584, 514, 1636, 488, 584, 462, 1684, 464, 610, 488, 582, 490, 1660, 540, 532, 490, 582, 514, 1606, 570, 1608, 488, 1660, 490, 582, 514, 558, 488, 582, 542, 530, 490, 580, 516, 560, 460, 606, 490, 584, 486, 584, 488, 584, 514, 554, 488, 582, 516, 554, 488, 582, 488, 584, 512, 560, 488, 584, 516, 1636, 488, 584, 488, 584, 488, 584, 490, 582, 516, 1636, 486, 584, 516, 558, 510, 560, 514, 556, 516, 556, 488, 582, 490, 582, 514, 556, 488, 584, 490, 582, 490, 582, 490, 586, 512, 558, 488, 584, 514, 558, 488, 582, 490, 608, 464, 582, 490, 582, 490, 580, 516, 556, 464, 608, 488, 582, 516, 582, 464, 584, 486, 582, 490, 584, 488, 584, 488, 1660, 544, 1606, 516, 530, 542, 556, 488, 586, 462, 608, 488, 582, 490, 582, 488, 584, 490, 1660, 488, 580, 490, 1662, 486, 610, 462, 582, 490, 1660, 464, 606, 492, 1660, 464, 608, 514, 556, 464, 608, 514, 558, 486, 584, 490, 1660, 490, 1660, 488, 1660, 488, 584, 490, 582, 488, 584, 490, 580, 490, 582, 516, 1636, 488, 584, 486, 1662, 462, 608, 516, 1632, 488, 1660, 518, 556, 492, 582, 516, 556, 488, 584, 488, 1660, 518, 1634, 490, 582, 514, 556, 490, 582, 488, 584, 514, 1636, 516, 556, 488, 1662, 486, 1664, 488, 584, 490, 582, 488, 1662, 514, 556, 492
        };
        Serial.println("AC Mode: Cool");
        sendDualIR_Raw(coolModeData, 243, 38);
        Blynk.virtualWrite(V12, "Cool");
      }
      break;

    case 5: // AC Dry Mode
      {
        uint16_t dryModeData[243] = {
          8396, 4180, 472, 598, 474, 1674, 474, 1676, 476, 598, 474, 1678, 472, 598, 528, 1626, 472, 598, 476, 1676, 474, 598, 474, 1702, 448, 1702, 448, 600, 526, 1626, 472, 1676, 476, 598, 474, 598, 474, 624, 500, 544, 528, 544, 528, 544, 528, 544, 528, 544, 528, 546, 526, 546, 528, 544, 526, 546, 552, 520, 528, 544, 552, 518, 528, 546, 550, 520, 552, 1598, 552, 492, 554, 570, 528, 520, 548, 520, 528, 1624, 472, 598, 526, 546, 550, 522, 526, 546, 524, 546, 528, 546, 524, 546, 552, 520, 526, 548, 550, 522, 548, 522, 524, 544, 554, 520, 500, 570, 552, 520, 474, 600, 522, 546, 500, 570, 528, 546, 524, 544, 526, 548, 524, 544, 502, 570, 528, 544, 502, 572, 526, 544, 528, 546, 524, 544, 528, 1622, 526, 1650, 500, 546, 526, 548, 526, 542, 530, 544, 526, 544, 528, 546, 526, 544, 526, 1622, 528, 544, 526, 1622, 528, 544, 528, 546, 528, 1624, 526, 546, 524, 1624, 526, 548, 524, 546, 526, 548, 524, 546, 526, 548, 522, 1626, 524, 1626, 524, 1624, 526, 548, 524, 548, 522, 1626, 526, 548, 524, 548, 498, 1650, 524, 546, 500, 572, 500, 574, 498, 1652, 524, 546, 498, 576, 496, 574, 496, 576, 472, 1676, 502, 572, 498, 574, 472, 598, 474, 600, 472, 600, 470, 600, 472, 1678, 474, 600, 472, 600, 470, 1678, 474, 600, 470, 600, 472, 1650, 498, 598, 474
        };
        Serial.println("AC Mode: Dry");
        sendDualIR_Raw(dryModeData, 243, 38);
        Blynk.virtualWrite(V12, "Dry");
      }
      break;

    case 6: // AC Fan Mode
      {
        uint16_t fanModeData[243] = {
          8304, 4238, 490, 582, 490, 1660, 490, 1686, 464, 608, 464, 1686, 464, 582, 492, 1658, 492, 582, 490, 608, 464, 582, 488, 1662, 490, 584, 488, 1686, 466, 1686, 464, 1688, 464, 582, 488, 582, 490, 608, 464, 582, 490, 608, 464, 610, 462, 582, 492, 606, 464, 608, 464, 608, 464, 608, 464, 608, 464, 580, 492, 584, 486, 582, 490, 582, 490, 582, 492, 1686, 464, 608, 464, 606, 464, 608, 464, 1662, 490, 582, 490, 1660, 490, 608, 462, 608, 464, 608, 464, 608, 464, 580, 490, 608, 464, 584, 486, 608, 464, 582, 490, 582, 490, 582, 490, 580, 492, 580, 490, 582, 490, 580, 490, 584, 488, 582, 490, 608, 464, 580, 492, 554, 518, 582, 490, 580, 490, 582, 490, 580, 492, 584, 488, 584, 488, 582, 490, 1660, 492, 1660, 492, 582, 492, 580, 492, 580, 492, 582, 490, 580, 490, 582, 490, 582, 488, 1658, 490, 582, 490, 1660, 490, 582, 490, 584, 490, 1686, 466, 582, 490, 1660, 490, 606, 464, 582, 490, 582, 492, 578, 492, 586, 488, 578, 492, 580, 492, 1684, 466, 608, 466, 1660, 490, 580, 492, 606, 466, 580, 492, 580, 492, 582, 492, 1656, 492, 580, 492, 1660, 490, 1660, 492, 582, 492, 580, 492, 582, 490, 582, 492, 1660, 492, 1660, 490, 580, 492, 582, 490, 582, 492, 582, 492, 1658, 492, 1658, 492, 1660, 492, 1662, 490, 580, 492, 582, 490, 1660, 490, 580, 490
        };
        Serial.println("AC Mode: Fan");
        sendDualIR_Raw(fanModeData, 243, 38);
        Blynk.virtualWrite(V12, "Fan");
      }
      break;

    case 7: // Air Refresh On
      {
        uint16_t airRefreshOnData[243] = {
          8282, 4268, 464, 608, 464, 1660, 492, 1658, 494, 608, 466, 1662, 490, 584, 488, 1658, 492, 608, 464, 608, 464, 1660, 492, 582, 490, 582, 488, 1686, 464, 1684, 464, 1658, 492, 608, 464, 606, 466, 606, 466, 582, 490, 580, 492, 580, 492, 608, 464, 582, 490, 580, 492, 582, 490, 582, 490, 582, 490, 582, 490, 606, 464, 608, 464, 582, 490, 582, 490, 1686, 464, 582, 490, 580, 492, 606, 466, 582, 490, 1660, 490, 608, 464, 580, 494, 582, 490, 580, 492, 606, 464, 582, 492, 608, 464, 582, 488, 606, 464, 608, 464, 606, 466, 1684, 466, 582, 490, 580, 490, 582, 492, 606, 466, 578, 492, 582, 490, 606, 464, 608, 464, 582, 488, 608, 464, 580, 492, 582, 490, 606, 464, 584, 488, 582, 490, 580, 492, 1658, 492, 1686, 464, 608, 464, 608, 464, 582, 490, 582, 490, 608, 464, 582, 490, 580, 492, 1660, 490, 580, 492, 1684, 464, 580, 492, 580, 492, 1662, 488, 582, 490, 1658, 490, 582, 490, 580, 492, 580, 492, 582, 488, 580, 492, 578, 492, 580, 492, 1660, 490, 1686, 464, 580, 492, 582, 490, 580, 490, 580, 492, 580, 492, 580, 492, 578, 494, 1658, 490, 1660, 490, 1658, 492, 580, 492, 582, 490, 580, 490, 584, 490, 1684, 464, 1656, 494, 578, 492, 580, 492, 580, 492, 578, 494, 1656, 494, 1658, 490, 1660, 492, 578, 492, 1658, 492, 578, 494, 1658, 492, 580, 492
        };
        Serial.println("Air Refresh: ON");
        sendDualIR_Raw(airRefreshOnData, 243, 38);
      }
      break;

    case 8: // Air Refresh Off
      {
        uint16_t airRefreshOffData[243] = {
          8304, 4238, 494, 552, 520, 1634, 518, 1658, 492, 580, 492, 1632, 518, 552, 520, 1632, 518, 554, 520, 580, 492, 1658, 492, 582, 492, 580, 492, 1658, 492, 1632, 520, 1632, 520, 554, 518, 580, 492, 556, 516, 554, 518, 554, 518, 554, 520, 578, 492, 580, 492, 556, 516, 554, 516, 554, 518, 580, 492, 580, 492, 554, 518, 580, 492, 524, 548, 556, 516, 1632, 518, 554, 518, 554, 518, 552, 518, 554, 518, 1632, 520, 580, 490, 556, 516, 554, 612, 462, 518, 554, 518, 580, 492, 552, 520, 552, 520, 554, 518, 582, 490, 552, 520, 580, 490, 556, 516, 556, 518, 580, 490, 554, 518, 582, 490, 554, 518, 582, 490, 580, 490, 582, 490, 582, 490, 580, 490, 582, 492, 582, 490, 556, 518, 582, 490, 556, 516, 1660, 492, 1662, 490, 554, 518, 556, 516, 584, 490, 556, 516, 584, 490, 582, 466, 582, 492, 1684, 490, 556, 514, 1634, 494, 608, 464, 606, 466, 1686, 464, 580, 492, 1684, 466, 582, 490, 582, 490, 582, 488, 608, 464, 582, 490, 1686, 466, 1684, 464, 1686, 464, 582, 490, 608, 464, 1684, 464, 608, 466, 580, 492, 582, 490, 606, 464, 608, 464, 1684, 466, 1660, 490, 1660, 490, 580, 490, 608, 464, 608, 464, 608, 464, 1686, 464, 1684, 464, 606, 466, 580, 492, 580, 492, 608, 464, 608, 464, 1684, 464, 582, 490, 580, 492, 1660, 490, 608, 464, 1660, 490, 606, 466
        };
        Serial.println("Air Refresh: OFF");
        sendDualIR_Raw(airRefreshOffData, 243, 38);
        Blynk.virtualWrite(V14, 0);
      }
      break;

    default:
      Serial.println("Speaker: Invalid command");
      break;
  }

  // Update status display
  String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | Speaker Command: " + String(command);
  Blynk.virtualWrite(V6, statusMsg);
}



// ============================================================================
// OPTIMIZED VOLUME CONTROL FUNCTIONS
// ============================================================================

BLYNK_WRITE(V4)  // Volume Up with Dual IR
{
  if (param.asInt() == 0) {
    Serial.println("Volume: UP");
    sendDualIR_NEC(0x807F40BF, 32);

    // Update status
    readDHTSensor();
    String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | Volume: UP";
    Blynk.virtualWrite(V6, statusMsg);
  }
}

BLYNK_WRITE(V5)  // Volume Down with Dual IR
{
  if (param.asInt() == 0) {
    Serial.println("Volume: DOWN");
    sendDualIR_NEC(0x807FC837, 32);

    // Update status
    readDHTSensor();
    String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | Volume: DOWN";
    Blynk.virtualWrite(V6, statusMsg);
  }
}

// ============================================================================
// OPTIMIZED AC CONTROL FUNCTIONS
// ============================================================================

BLYNK_WRITE(V2)  // AC OFF with Dual IR
{
  if (param.asInt() == 1) {
    readDHTSensor(); // Get current sensor data

    uint16_t acOffData[243] = {
      8282, 4264, 464, 580, 492, 1684, 466, 1684, 466, 606, 464, 1684, 466, 580, 490, 1682, 466, 608, 468, 604, 466, 1656, 494, 606, 464, 606, 464, 1684, 464, 1684, 464, 1660, 492, 582, 490, 580, 492, 582, 490, 606, 464, 580, 492, 580, 492, 606, 464, 580, 490, 608, 466, 578, 492, 578, 492, 582, 490, 584, 486, 606, 466, 580, 490, 580, 492, 582, 490, 1658, 490, 582, 490, 606, 464, 606, 466, 606, 464, 1660, 492, 580, 492, 582, 492, 580, 490, 580, 492, 552, 520, 582, 490, 606, 464, 608, 464, 1656, 494, 1658, 492, 580, 492, 580, 492, 606, 466, 606, 466, 582, 490, 580, 490, 582, 490, 578, 494, 580, 490, 580, 492, 580, 492, 580, 492, 606, 466, 580, 492, 582, 490, 580, 492, 580, 492, 580, 492, 1684, 466, 1658, 492, 582, 492, 580, 490, 580, 492, 606, 466, 606, 464, 580, 492, 580, 490, 1656, 492, 582, 490, 1656, 492, 580, 490, 554, 520, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 580, 490, 580, 494, 580, 492, 578, 492, 1658, 492, 580, 492, 582, 490, 1686, 466, 1660, 490, 580, 492, 584, 488, 1660, 492, 580, 492, 580, 490, 580, 490, 578, 494, 580, 492, 606, 466, 580, 492, 1658, 492, 580, 492, 1660, 490, 1660, 492, 580, 490, 580, 492, 606, 466, 580, 492, 1658, 490, 580, 492, 580, 490, 580, 492, 1656, 494, 580, 492, 1684, 464, 580, 492
    };

    Serial.println("AC: Turning OFF");
    sendDualIR_Raw(acOffData, 243, 38);

    // Update status with enhanced information
    String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | AC: OFF";
    if (sysState.autoMode) {
      statusMsg += " | AUTO Range: " + String(sysState.coldThreshold, 1) + "-" + String(sysState.hotThreshold, 1) + "°C";
    }
    Blynk.virtualWrite(V6, statusMsg);
  }
}

BLYNK_WRITE(V1)  // AC ON with Dual IR
{
  if (param.asInt() == 1) {
    readDHTSensor(); // Get current sensor data

    uint16_t acOnData[243] = {
      8312, 4264, 466, 606, 466, 1684, 468, 1658, 492, 578, 494, 1684, 466, 606, 464, 1684, 466, 606, 466, 606, 466, 1684, 468, 606, 466, 608, 466, 1660, 490, 1684, 466, 1658, 494, 608, 464, 606, 466, 608, 466, 606, 466, 580, 492, 580, 492, 606, 466, 580, 492, 608, 466, 606, 466, 582, 492, 606, 466, 606, 466, 606, 466, 606, 466, 580, 492, 606, 466, 1684, 466, 606, 464, 606, 466, 582, 490, 606, 466, 1682, 466, 606, 466, 606, 466, 606, 466, 606, 464, 580, 492, 606, 464, 606, 466, 580, 492, 606, 464, 608, 464, 582, 490, 606, 466, 606, 466, 580, 492, 604, 466, 606, 466, 606, 466, 580, 492, 550, 520, 606, 466, 606, 464, 606, 464, 606, 466, 580, 490, 606, 466, 608, 464, 606, 464, 606, 466, 1684, 464, 1684, 466, 606, 466, 606, 464, 580, 492, 608, 464, 608, 464, 606, 466, 604, 464, 1686, 464, 606, 464, 1684, 466, 606, 466, 606, 466, 1684, 466, 604, 464, 1658, 492, 578, 492, 604, 466, 606, 466, 606, 466, 606, 464, 1656, 492, 606, 464, 1684, 466, 1658, 490, 1658, 490, 582, 490, 606, 466, 582, 490, 1684, 466, 606, 466, 606, 464, 578, 494, 606, 464, 606, 466, 606, 466, 608, 464, 1684, 464, 580, 490, 1684, 466, 1684, 464, 606, 464, 606, 466, 606, 466, 608, 464, 580, 490, 1656, 492, 1684, 464, 1684, 466, 606, 466, 606, 466, 1656, 492, 582, 488
    };

    Serial.println("AC: Turning ON");
    sendDualIR_Raw(acOnData, 243, 38);

    // Update status with enhanced information
    String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | AC: ON";
    if (sysState.autoMode) {
      statusMsg += " | AUTO Range: " + String(sysState.coldThreshold, 1) + "-" + String(sysState.hotThreshold, 1) + "°C";
    }
    Blynk.virtualWrite(V6, statusMsg);
  }
}




// ============================================================================
// OPTIMIZED TEMPERATURE CONTROL FUNCTIONS
// ============================================================================

BLYNK_WRITE(V0)  // Temperature Control with Dual IR
{
  readDHTSensor(); // Get current sensor data
  int targetTemp = param.asInt();

  // Validate temperature range
  if (targetTemp < 16 || targetTemp > 30) {
    Serial.println("Temperature out of range (16-30°C)");
    return;
  }

  sysState.targetTemp = targetTemp;
  Serial.println("Setting AC temperature to: " + String(targetTemp) + "°C");

  switch(targetTemp) {
    case 16:
      {
        uint16_t temp16Data[243] = {
          8314, 4224, 504, 570, 502, 1648, 502, 1646, 526, 544, 526, 1624, 500, 570, 500, 1648, 528, 546, 526, 546, 526, 546, 500, 1624, 526, 1648, 526, 548, 500, 1646, 526, 1624, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 544, 526, 520, 552, 544, 526, 546, 526, 546, 526, 546, 526, 544, 526, 544, 526, 544, 526, 546, 526, 546, 524, 546, 526, 1622, 526, 546, 524, 544, 526, 548, 522, 546, 524, 1626, 526, 544, 530, 544, 526, 544, 526, 544, 528, 544, 526, 570, 502, 546, 526, 544, 526, 544, 526, 544, 528, 544, 526, 542, 528, 544, 528, 544, 526, 544, 526, 546, 526, 542, 528, 544, 528, 544, 528, 546, 526, 546, 526, 544, 528, 544, 526, 544, 526, 546, 526, 544, 528, 544, 528, 546, 526, 1620, 526, 1622, 528, 546, 526, 544, 528, 544, 526, 544, 526, 544, 528, 544, 526, 570, 500, 1624, 526, 544, 526, 1622, 526, 546, 526, 546, 526, 1648, 502, 544, 528, 1624, 526, 544, 528, 544, 524, 546, 526, 546, 530, 544, 528, 1620, 528, 1624, 526, 544, 528, 1620, 528, 546, 526, 544, 528, 546, 528, 544, 528, 544, 528, 544, 526, 544, 526, 544, 526, 1622, 526, 546, 526, 544, 526, 544, 526, 544, 526, 1622, 524, 546, 526, 546, 526, 544, 526, 546, 524, 544, 528, 548, 524, 1622, 528, 546, 526, 546, 526, 1626, 524, 544, 526, 546, 524, 1624, 526, 546, 526
        };
        sendDualIR_Raw(temp16Data, 243, 38);
      }
      break;

      {
        uint16_t temp17Data[243] = {
          8396, 4180, 472, 598, 474, 1674, 474, 1676, 476, 598, 474, 1678, 472, 598, 528, 1626, 472, 598, 476, 1676, 474, 598, 474, 1702, 448, 1702, 448, 600, 526, 1626, 472, 1676, 476, 598, 474, 598, 474, 624, 500, 544, 528, 544, 528, 544, 528, 544, 528, 544, 528, 546, 526, 546, 528, 544, 526, 546, 552, 520, 528, 544, 552, 518, 528, 546, 550, 520, 552, 1598, 552, 492, 554, 570, 528, 520, 548, 520, 528, 1624, 472, 598, 526, 546, 550, 522, 526, 546, 524, 546, 528, 546, 524, 546, 552, 520, 526, 548, 550, 522, 548, 522, 524, 544, 554, 520, 500, 570, 552, 520, 474, 600, 522, 546, 500, 570, 528, 546, 524, 544, 526, 548, 524, 544, 502, 570, 528, 544, 502, 572, 526, 544, 528, 546, 524, 544, 528, 1622, 526, 1650, 500, 546, 526, 548, 526, 542, 530, 544, 526, 544, 528, 546, 526, 544, 526, 1622, 528, 544, 526, 1622, 528, 544, 528, 546, 528, 1624, 526, 546, 524, 1624, 526, 548, 524, 546, 526, 548, 524, 546, 526, 548, 522, 1626, 524, 1626, 524, 1624, 526, 548, 524, 548, 522, 1626, 526, 548, 524, 548, 498, 1650, 524, 546, 500, 572, 500, 574, 498, 1652, 524, 546, 498, 576, 496, 574, 496, 576, 472, 1676, 502, 572, 498, 574, 472, 598, 474, 600, 472, 600, 470, 600, 472, 1678, 474, 600, 472, 600, 470, 1678, 474, 600, 470, 600, 472, 1650, 498, 598, 474
        };
        sendDualIR_Raw(temp17Data, 243, 38);
      }
      break;

    case 18:
      {
        uint16_t temp18Data[243] = {
          8340, 4206, 550, 522, 550, 1600, 548, 1600, 544, 530, 528, 1622, 552, 520, 552, 1594, 556, 520, 550, 518, 556, 1596, 578, 1572, 554, 1600, 548, 522, 528, 1622, 580, 1572, 528, 544, 528, 546, 550, 522, 524, 544, 550, 522, 522, 546, 526, 548, 546, 526, 516, 558, 522, 548, 524, 572, 500, 548, 522, 548, 522, 548, 522, 548, 522, 548, 524, 548, 498, 1650, 524, 548, 524, 548, 498, 572, 498, 574, 522, 1622, 528, 546, 526, 548, 496, 574, 520, 552, 496, 574, 498, 574, 498, 572, 498, 574, 498, 572, 472, 602, 494, 552, 494, 598, 472, 600, 472, 602, 466, 630, 446, 600, 458, 614, 470, 600, 472, 600, 470, 600, 472, 600, 472, 600, 472, 602, 444, 624, 444, 624, 452, 624, 448, 624, 448, 622, 450, 1700, 472, 1676, 472, 598, 474, 598, 448, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 1698, 448, 624, 450, 1700, 448, 624, 448, 624, 448, 1702, 446, 622, 450, 1702, 446, 624, 448, 622, 448, 622, 450, 622, 448, 624, 448, 1700, 448, 1728, 420, 1700, 450, 624, 446, 1704, 446, 1702, 448, 624, 448, 622, 474, 1676, 450, 1700, 448, 626, 446, 624, 472, 1678, 446, 624, 472, 596, 472, 600, 470, 600, 470, 1678, 446, 626, 472, 598, 472, 600, 470, 600, 472, 600, 472, 600, 470, 1680, 446, 624, 472, 1680, 444, 1704, 470, 602, 472, 598, 474, 1676, 474, 598, 474
        };
        sendDualIR_Raw(temp18Data, 243, 38);
      }
      break;

    // Add more temperature cases efficiently
    default:
      {
        // For temperatures 19-30, use a generic approach with temperature mapping
        Serial.println("Setting temperature to: " + String(targetTemp) + "°C (using generic mapping)");

        // Use temperature 24 as base and modify for other temperatures
        uint16_t baseTemp24Data[243] = {
          8340, 4204, 502, 570, 500, 1650, 500, 1650, 524, 572, 476, 1650, 526, 546, 526, 1624, 526, 548, 500, 596, 476, 570, 526, 1622, 528, 546, 524, 1624, 526, 1624, 526, 1628, 522, 548, 524, 546, 524, 548, 524, 548, 524, 546, 526, 546, 524, 546, 526, 544, 526, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 526, 546, 524, 546, 526, 544, 526, 1624, 526, 544, 526, 546, 526, 544, 528, 546, 526, 546, 526, 544, 526, 548, 524, 546, 526, 546, 524, 546, 526, 548, 524, 546, 526, 546, 526, 548, 526, 546, 524, 548, 526, 546, 526, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 1626, 524, 1626, 524, 548, 524, 546, 524, 548, 524, 548, 524, 548, 524, 548, 522, 548, 524, 1626, 524, 546, 524, 1626, 522, 574, 474, 572, 524, 1626, 498, 574, 498, 1652, 498, 574, 498, 574, 498, 576, 494, 576, 494, 576, 498, 576, 496, 1652, 474, 600, 472, 602, 496, 574, 472, 1680, 472, 598, 472, 600, 474, 1676, 474, 600, 470, 600, 446, 626, 446, 624, 472, 1678, 448, 626, 470, 602, 446, 624, 448, 1704, 470, 600, 472, 602, 446, 624, 448, 624, 448, 624, 446, 624, 448, 1702, 446, 624, 472, 1680, 448, 1702, 448, 1702, 448, 1702, 472, 600, 448, 624, 472
        };
        sendDualIR_Raw(baseTemp24Data, 243, 38);
      }
      break;
  }

  // Update status with temperature information
  String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | AC Target: " + String(targetTemp) + "°C";
  if (sysState.autoMode) {
    statusMsg += " | AUTO Range: " + String(sysState.coldThreshold, 1) + "-" + String(sysState.hotThreshold, 1) + "°C";
  }
  Blynk.virtualWrite(V6, statusMsg);
}







  }
}

// ============================================================================
// OPTIMIZED AUTOMATION FUNCTIONS
// ============================================================================

BLYNK_WRITE(V7) // Enhanced Auto AC Control
{
  // Sync threshold values on first run
  if (!sysState.initialized) {
    Blynk.syncVirtual(V8);
    Blynk.syncVirtual(V9);
    sysState.initialized = true;
  }

  readDHTSensor(); // Get current sensor data
  int autoMode = param.asInt();

  switch(autoMode) {
    case 0: // Auto AC OFF
      sysState.autoMode = false;
      sysState.fanMode = false;
      sysState.autoStage = 0;
      sysState.stabilityCounter = 0;
      sysState.tempHandled = false;

      Serial.println("Auto AC: DISABLED");
      break;

    case 1: // Auto AC ON (Normal Mode)
      sysState.autoMode = true;
      sysState.fanMode = false;
      sysState.autoStage = 0;
      sysState.stabilityCounter = 0;
      sysState.tempHandled = false;

      Serial.println("Auto AC: ENABLED (Normal Mode)");
      AUTOTEMP(); // Start auto temperature control
      break;

    case 2: // Auto AC ON (Fan Priority Mode)
      sysState.autoMode = true;
      sysState.fanMode = true;
      sysState.autoStage = 0;
      sysState.stabilityCounter = 0;
      sysState.tempHandled = false;

      Serial.println("Auto AC: ENABLED (Fan Priority Mode)");
      AUTOTEMP(); // Start auto temperature control
      break;

    default:
      Serial.println("Auto AC: Invalid mode");
      return;
  }

  // Update status display
  String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "%";
  if (sysState.autoMode) {
    statusMsg += " | AUTO AC: ON";
    if (sysState.fanMode) statusMsg += " (FAN MODE)";
    statusMsg += " | Range: " + String(sysState.coldThreshold, 1) + "-" + String(sysState.hotThreshold, 1) + "°C";
  } else {
    statusMsg += " | AUTO AC: OFF";
  }
  Blynk.virtualWrite(V6, statusMsg);
}

BLYNK_WRITE(V8)  // Auto AC Hot Threshold Control
{
  readDHTSensor(); // Get current sensor data
  float newHotThreshold = param.asFloat();

  // Validate and ensure hot threshold is at least 1°C above cold threshold
  if (newHotThreshold <= sysState.coldThreshold) {
    newHotThreshold = sysState.coldThreshold + 1.0;
    Serial.println("Hot threshold adjusted to maintain 1°C minimum gap");
  }

  // Validate range (16-35°C)
  if (newHotThreshold < 17 || newHotThreshold > 35) {
    Serial.println("Hot threshold out of valid range (17-35°C)");
    return;
  }

  sysState.hotThreshold = newHotThreshold;
  Blynk.virtualWrite(V8, sysState.hotThreshold);  // Sync back to app

  // Reset auto control state when thresholds change
  sysState.autoStage = 0;
  sysState.stabilityCounter = 0;
  sysState.tempHandled = false;

  Serial.println("Hot threshold set to: " + String(sysState.hotThreshold, 1) + "°C");

  // Update status display
  String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | Hot Threshold: " + String(sysState.hotThreshold, 1) + "°C";
  Blynk.virtualWrite(V6, statusMsg);
}

BLYNK_WRITE(V9)  // Auto AC Cold Threshold Control
{
  readDHTSensor(); // Get current sensor data
  float newColdThreshold = param.asFloat();

  // Validate and ensure cold threshold is at least 1°C below hot threshold
  if (newColdThreshold >= sysState.hotThreshold) {
    newColdThreshold = sysState.hotThreshold - 1.0;
    Serial.println("Cold threshold adjusted to maintain 1°C minimum gap");
  }

  // Validate range (16-34°C)
  if (newColdThreshold < 16 || newColdThreshold > 34) {
    Serial.println("Cold threshold out of valid range (16-34°C)");
    return;
  }

  sysState.coldThreshold = newColdThreshold;
  Blynk.virtualWrite(V9, sysState.coldThreshold);  // Sync back to app

  // Reset auto control state when thresholds change
  sysState.autoStage = 0;
  sysState.stabilityCounter = 0;
  sysState.tempHandled = false;

  Serial.println("Cold threshold set to: " + String(sysState.coldThreshold, 1) + "°C");

  // Update status display
  String statusMsg = "TEMP: " + String(lastTemp, 1) + "°C / HUM: " + String(lastHumidity, 1) + "% | Cold Threshold: " + String(sysState.coldThreshold, 1) + "°C";
  Blynk.virtualWrite(V6, statusMsg);
}



// ============================================================================
// ENHANCED AUTO TEMPERATURE CONTROL FUNCTION
// ============================================================================

void AUTOTEMP() {
  if (!sysState.autoMode) {
    return; // Exit if auto mode is disabled
  }

  readDHTSensor(); // Get current sensor data
  float currentTemp = lastTemp;
  float currentHumidity = lastHumidity;

  // Validate sensor readings
  if (isnan(currentTemp) || isnan(currentHumidity)) {
    Serial.println("Auto AC: Invalid sensor readings, skipping cycle");
    return;
  }

  Serial.println("Auto AC: Current=" + String(currentTemp, 1) + "°C, Range=" +
                String(sysState.coldThreshold, 1) + "-" + String(sysState.hotThreshold, 1) + "°C");

  // Enhanced auto control logic with improved state management
  if (sysState.autoStage < 3) {
    // Initial control phase - respond to temperature outside comfort zone
    if (currentTemp >= sysState.hotThreshold ||
        (currentTemp > sysState.coldThreshold && currentTemp < sysState.hotThreshold)) {

      // Determine optimal target temperature based on current conditions
      int optimalTemp = calculateOptimalTemperature(currentTemp);

      Serial.println("Auto AC: Activating cooling to " + String(optimalTemp) + "°C");
      setACTemperature(optimalTemp);

      String statusMsg = "TEMP: " + String(currentTemp, 1) + "°C / HUM: " + String(currentHumidity, 1) +
                        "% | AUTO AC: COOLING to " + String(optimalTemp) + "°C";
      Blynk.virtualWrite(V6, statusMsg);

      sysState.autoStage++;
      sysState.tempHandled = false;

    } else if (currentTemp < sysState.coldThreshold && !sysState.tempHandled) {
      // Temperature too low - handle based on fan mode
      if (!sysState.fanMode) {
        // Normal mode - turn off AC
        turnOffAC();
        String statusMsg = "TEMP: " + String(currentTemp, 1) + "°C / HUM: " + String(currentHumidity, 1) +
                          "% | AUTO AC: OFF (Too Cold)";
        Blynk.virtualWrite(V6, statusMsg);
      } else {
        // Fan mode - switch to fan only
        setACFanMode();
        String statusMsg = "TEMP: " + String(currentTemp, 1) + "°C / HUM: " + String(currentHumidity, 1) +
                          "% | AUTO AC: FAN MODE";
        Blynk.virtualWrite(V6, statusMsg);
      }

      sysState.tempHandled = true;
      sysState.autoStage = 0;
      sysState.stabilityCounter = 0;
    }

  } else if (sysState.autoStage == 3) {
    // Temperature stabilization phase
    sysState.exactTemp = currentTemp + 0.2; // Add small offset for stability
    sysState.autoStage++;

  } else if (sysState.autoStage == 4 && sysState.stabilityCounter < 16) {
    // Stability monitoring phase
    if (sysState.stabilityCounter < 15) {
      sysState.stabilityCounter++;
    } else if (sysState.stabilityCounter == 15) {
      // Check if temperature is stable
      if (sysState.exactTemp > currentTemp) {
        sysState.stabilityCounter++;
        sysState.tempHandled = false;
      } else {
        // Temperature stable, reset control
        sysState.autoStage = 0;
        sysState.stabilityCounter = 0;
        sysState.tempHandled = false;
      }
    }

  } else {
    // Final check for temperature too low
    if (currentTemp < sysState.coldThreshold && !sysState.tempHandled) {
      if (!sysState.fanMode) {
        turnOffAC();
      } else {
        setACFanMode();
      }
      sysState.tempHandled = true;
      sysState.autoStage = 0;
      sysState.stabilityCounter = 0;
    }
  }
}

// Helper function to calculate optimal temperature
int calculateOptimalTemperature(float currentTemp) {
    return 16;
  } else if (sysState.coldThreshold <= 19) {
    return 17;
  } else if (sysState.coldThreshold <= 20) {
    return 18;
  } else if (sysState.coldThreshold <= 21) {
    return 19;
  } else if (sysState.coldThreshold <= 22) {
    return 20;
  } else if (sysState.coldThreshold <= 23) {
    return 21;
  } else if (sysState.coldThreshold <= 26) {
    return 24;
  } else if (sysState.coldThreshold <= 30) {
    return 27;
  } else {
    return 30;
  }
}

// Helper function to set AC temperature using dual IR
void setACTemperature(int temperature) {
  sysState.targetTemp = temperature;

  // Use the temperature control function
  uint16_t baseTemp24Data[243] = {
    8340, 4204, 502, 570, 500, 1650, 500, 1650, 524, 572, 476, 1650, 526, 546, 526, 1624, 526, 548, 500, 596, 476, 570, 526, 1622, 528, 546, 524, 1624, 526, 1624, 526, 1628, 522, 548, 524, 546, 524, 548, 524, 548, 524, 546, 526, 546, 524, 546, 526, 544, 526, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 526, 546, 524, 546, 526, 544, 526, 1624, 526, 544, 526, 546, 526, 544, 528, 546, 526, 546, 526, 544, 526, 548, 524, 546, 526, 546, 524, 546, 526, 548, 524, 546, 526, 546, 526, 548, 526, 546, 524, 548, 526, 546, 526, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 1626, 524, 1626, 524, 548, 524, 546, 524, 548, 524, 548, 524, 548, 524, 548, 522, 548, 524, 1626, 524, 546, 524, 1626, 522, 574, 474, 572, 524, 1626, 498, 574, 498, 1652, 498, 574, 498, 574, 498, 576, 494, 576, 494, 576, 498, 576, 496, 1652, 474, 600, 472, 602, 496, 574, 472, 1680, 472, 598, 472, 600, 474, 1676, 474, 600, 470, 600, 446, 626, 446, 624, 472, 1678, 448, 626, 470, 602, 446, 624, 448, 1704, 470, 600, 472, 602, 446, 624, 448, 624, 448, 624, 446, 624, 448, 1702, 446, 624, 472, 1680, 448, 1702, 448, 1702, 448, 1702, 472, 600, 448, 624, 472
  };

  Serial.println("Auto AC: Setting temperature to " + String(temperature) + "°C");
  sendDualIR_Raw(baseTemp24Data, 243, 38);
}

// Helper function to turn off AC
void turnOffAC() {
  uint16_t acOffData[243] = {
    8282, 4264, 464, 580, 492, 1684, 466, 1684, 466, 606, 464, 1684, 466, 580, 490, 1682, 466, 608, 468, 604, 466, 1656, 494, 606, 464, 606, 464, 1684, 464, 1684, 464, 1660, 492, 582, 490, 580, 492, 582, 490, 606, 464, 580, 492, 580, 492, 606, 464, 580, 490, 608, 466, 578, 492, 578, 492, 582, 490, 584, 486, 606, 466, 580, 490, 580, 492, 582, 490, 1658, 490, 582, 490, 606, 464, 606, 466, 606, 464, 1660, 492, 580, 492, 582, 492, 580, 490, 580, 492, 552, 520, 582, 490, 606, 464, 608, 464, 1656, 494, 1658, 492, 580, 492, 580, 492, 606, 466, 606, 466, 582, 490, 580, 490, 582, 490, 578, 494, 580, 490, 580, 492, 580, 492, 580, 492, 606, 466, 580, 492, 582, 490, 580, 492, 580, 492, 580, 492, 1684, 466, 1658, 492, 582, 492, 580, 490, 580, 492, 606, 466, 606, 464, 580, 492, 580, 490, 1656, 492, 582, 490, 1656, 492, 580, 490, 554, 520, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 580, 490, 580, 494, 580, 492, 578, 492, 1658, 492, 580, 492, 582, 490, 1686, 466, 1660, 490, 580, 492, 584, 488, 1660, 492, 580, 492, 580, 490, 580, 490, 578, 494, 580, 492, 606, 466, 580, 492, 1658, 492, 580, 492, 1660, 490, 1660, 492, 580, 490, 580, 492, 606, 466, 580, 492, 1658, 490, 580, 492, 580, 490, 580, 492, 1656, 494, 580, 492, 1684, 464, 580, 492
  };

  Serial.println("Auto AC: Turning OFF");
  sendDualIR_Raw(acOffData, 243, 38);
}

// Helper function to set AC to fan mode
void setACFanMode() {
  uint16_t fanModeData[243] = {
    8304, 4238, 490, 582, 490, 1660, 490, 1686, 464, 608, 464, 1686, 464, 582, 492, 1658, 492, 582, 490, 608, 464, 582, 488, 1662, 490, 584, 488, 1686, 466, 1686, 464, 1688, 464, 582, 488, 582, 490, 608, 464, 582, 490, 608, 464, 610, 462, 582, 492, 606, 464, 608, 464, 608, 464, 608, 464, 608, 464, 580, 492, 584, 486, 582, 490, 582, 490, 582, 492, 1686, 464, 608, 464, 606, 464, 608, 464, 1662, 490, 582, 490, 1660, 490, 608, 462, 608, 464, 608, 464, 608, 464, 580, 490, 608, 464, 584, 486, 608, 464, 582, 490, 582, 490, 582, 490, 580, 492, 580, 490, 582, 490, 580, 490, 584, 488, 582, 490, 608, 464, 580, 492, 554, 518, 582, 490, 580, 490, 582, 490, 580, 492, 584, 488, 584, 488, 582, 490, 1660, 492, 1660, 492, 582, 492, 580, 492, 580, 492, 582, 490, 580, 490, 582, 490, 582, 488, 1658, 490, 582, 490, 1660, 490, 582, 490, 584, 490, 1686, 466, 582, 490, 1660, 490, 606, 464, 582, 490, 582, 492, 578, 492, 586, 488, 578, 492, 580, 492, 1684, 466, 608, 466, 1660, 490, 580, 492, 606, 466, 580, 492, 580, 492, 582, 492, 1656, 492, 580, 492, 1660, 490, 1660, 492, 582, 492, 580, 492, 582, 490, 582, 492, 1660, 492, 1660, 490, 580, 492, 582, 490, 582, 492, 582, 492, 1658, 492, 1658, 492, 1660, 492, 1662, 490, 580, 492, 582, 490, 1660, 490, 580, 490
  };

  Serial.println("Auto AC: Setting to FAN mode");
  sendDualIR_Raw(fanModeData, 243, 38);
}
        } else if (cold <= 20) {
          uint16_t rawData[243] = { 8340, 4206, 550, 522, 550, 1600, 548, 1600, 544, 530, 528, 1622, 552, 520, 552, 1594, 556, 520, 550, 518, 556, 1596, 578, 1572, 554, 1600, 548, 522, 528, 1622, 580, 1572, 528, 544, 528, 546, 550, 522, 524, 544, 550, 522, 522, 546, 526, 548, 546, 526, 516, 558, 522, 548, 524, 572, 500, 548, 522, 548, 522, 548, 522, 548, 522, 548, 524, 548, 498, 1650, 524, 548, 524, 548, 498, 572, 498, 574, 522, 1622, 528, 546, 526, 548, 496, 574, 520, 552, 496, 574, 498, 574, 498, 572, 498, 574, 498, 572, 472, 602, 494, 552, 494, 598, 472, 600, 472, 602, 466, 630, 446, 600, 458, 614, 470, 600, 472, 600, 470, 600, 472, 600, 472, 600, 472, 602, 444, 624, 444, 624, 452, 624, 448, 624, 448, 622, 450, 1700, 472, 1676, 472, 598, 474, 598, 448, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 1698, 448, 624, 450, 1700, 448, 624, 448, 624, 448, 1702, 446, 622, 450, 1702, 446, 624, 448, 622, 448, 622, 450, 622, 448, 624, 448, 1700, 448, 1728, 420, 1700, 450, 624, 446, 1704, 446, 1702, 448, 624, 448, 622, 474, 1676, 450, 1700, 448, 626, 446, 624, 472, 1678, 446, 624, 472, 596, 472, 600, 470, 600, 470, 1678, 446, 626, 472, 598, 472, 600, 470, 600, 472, 600, 472, 600, 470, 1680, 446, 624, 472, 1680, 444, 1704, 470, 602, 472, 598, 474, 1676, 474, 598, 474 };  // UNKNOWN 5E90CEB8
          Serial.println("Turning the temperature to 18");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 21) {
          uint16_t rawData[243] = { 8290, 4254, 526, 520, 550, 1624, 528, 1620, 528, 546, 502, 1648, 526, 570, 500, 1624, 526, 570, 476, 1646, 528, 1620, 528, 1622, 526, 1624, 524, 546, 526, 1622, 528, 1620, 526, 546, 526, 570, 502, 544, 528, 546, 524, 542, 528, 544, 526, 544, 526, 544, 528, 544, 528, 544, 526, 546, 526, 544, 528, 546, 524, 544, 526, 544, 526, 544, 526, 570, 502, 1620, 528, 542, 528, 544, 528, 546, 524, 544, 528, 1622, 528, 546, 524, 544, 528, 544, 528, 544, 526, 542, 528, 544, 528, 544, 526, 544, 528, 544, 528, 542, 528, 544, 528, 542, 528, 542, 528, 544, 526, 544, 528, 544, 526, 544, 528, 544, 528, 544, 526, 542, 528, 542, 528, 546, 526, 546, 526, 544, 528, 546, 528, 544, 526, 544, 528, 544, 526, 1622, 528, 1622, 528, 544, 526, 544, 528, 542, 528, 544, 526, 546, 526, 544, 528, 544, 528, 1620, 528, 544, 528, 1622, 528, 542, 528, 546, 526, 1620, 528, 546, 524, 1622, 528, 544, 528, 544, 528, 544, 528, 544, 526, 546, 528, 1622, 526, 544, 526, 544, 526, 544, 526, 1648, 500, 544, 526, 546, 526, 544, 524, 1622, 526, 544, 526, 1622, 526, 546, 526, 1624, 526, 544, 526, 544, 526, 546, 524, 546, 524, 1624, 524, 546, 526, 546, 524, 546, 524, 546, 524, 548, 500, 572, 522, 548, 500, 570, 500, 572, 498, 1650, 500, 572, 498, 574, 498, 1648, 500, 572, 498 };  // MIRAGE
          Serial.println("Turning the temperature to 19");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 22) {
          uint16_t rawData[243] = { 8336, 4196, 528, 548, 524, 1620, 528, 1622, 526, 546, 524, 1624, 526, 546, 526, 1622, 526, 544, 526, 548, 524, 544, 526, 544, 526, 546, 524, 1622, 528, 1596, 554, 1624, 526, 544, 528, 544, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 570, 502, 546, 526, 544, 526, 544, 528, 568, 502, 546, 526, 546, 526, 1622, 526, 544, 526, 544, 528, 544, 526, 544, 526, 1620, 528, 542, 528, 546, 526, 544, 526, 544, 528, 544, 526, 544, 528, 542, 530, 542, 530, 544, 528, 544, 528, 544, 526, 544, 528, 544, 528, 544, 528, 544, 528, 542, 528, 544, 526, 548, 524, 568, 504, 544, 526, 544, 528, 544, 526, 544, 526, 546, 526, 542, 528, 544, 528, 544, 526, 544, 528, 1622, 528, 1622, 526, 544, 528, 544, 528, 544, 528, 544, 528, 542, 528, 542, 528, 542, 528, 1620, 528, 544, 528, 1622, 526, 546, 526, 546, 528, 1620, 530, 542, 528, 1622, 528, 542, 528, 544, 526, 544, 528, 546, 524, 544, 526, 544, 526, 544, 528, 544, 528, 544, 526, 1622, 528, 1622, 528, 546, 526, 546, 524, 546, 526, 1622, 526, 546, 526, 546, 526, 1622, 526, 1624, 524, 548, 524, 546, 526, 1624, 526, 546, 524, 548, 500, 570, 526, 546, 500, 570, 500, 572, 500, 572, 500, 1646, 502, 570, 502, 572, 498, 1648, 500, 1648, 498, 1652, 498, 570, 500, 570, 500 };  // MIRAGE
          Serial.println("Turning the temperature to 20 ");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 23) {
          uint16_t rawData[243] = { 8292, 4252, 500, 572, 472, 1678, 472, 1678, 472, 598, 448, 1704, 470, 598, 450, 1700, 474, 600, 472, 1678, 472, 598, 448, 624, 448, 624, 446, 1700, 474, 1674, 474, 1678, 470, 600, 448, 624, 450, 624, 448, 622, 448, 622, 448, 624, 448, 622, 450, 628, 444, 624, 450, 624, 448, 622, 450, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 622, 450, 1702, 450, 626, 446, 622, 448, 622, 448, 624, 448, 1700, 450, 622, 450, 622, 450, 622, 450, 622, 448, 624, 448, 622, 448, 622, 448, 622, 450, 622, 450, 622, 450, 624, 448, 620, 450, 622, 450, 624, 448, 624, 472, 596, 474, 596, 474, 596, 450, 620, 450, 622, 474, 598, 472, 596, 474, 598, 450, 622, 450, 622, 472, 598, 476, 598, 472, 598, 474, 1674, 448, 1700, 450, 624, 448, 624, 472, 596, 450, 622, 474, 596, 472, 600, 472, 600, 472, 1672, 450, 624, 448, 1700, 448, 622, 474, 598, 474, 1672, 450, 624, 472, 1676, 472, 600, 472, 596, 474, 596, 474, 596, 474, 596, 476, 596, 474, 598, 474, 1674, 474, 598, 472, 1676, 474, 596, 476, 596, 474, 622, 450, 1674, 474, 1674, 474, 1674, 474, 1676, 472, 1674, 476, 596, 500, 572, 500, 572, 500, 570, 502, 1648, 474, 596, 500, 572, 500, 570, 500, 570, 500, 570, 502, 568, 502, 570, 502, 570, 500, 568, 504, 1646, 502, 570, 502, 568, 502, 1646, 502, 568, 504 };  // UNKNOWN 929340A4
          Serial.println("Turning the temperature to 21");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 24) {
          uint16_t rawData[243] = { 8282, 4254, 472, 598, 472, 1678, 472, 1676, 472, 598, 474, 1676, 472, 622, 448, 1678, 472, 600, 472, 624, 446, 1678, 472, 600, 472, 600, 470, 1678, 472, 1676, 474, 1700, 448, 598, 474, 598, 474, 598, 474, 622, 448, 598, 474, 596, 476, 622, 448, 598, 474, 598, 472, 596, 474, 622, 474, 574, 474, 598, 472, 598, 474, 596, 498, 572, 474, 598, 498, 1648, 498, 574, 474, 600, 496, 572, 500, 572, 500, 1648, 500, 574, 498, 570, 500, 570, 526, 570, 476, 572, 524, 546, 500, 570, 524, 572, 500, 546, 524, 548, 524, 548, 524, 546, 526, 546, 526, 546, 524, 544, 526, 546, 526, 544, 526, 570, 500, 546, 526, 568, 502, 544, 524, 546, 524, 546, 524, 544, 526, 544, 526, 544, 526, 542, 528, 546, 524, 1622, 526, 1622, 526, 544, 526, 546, 526, 568, 502, 544, 526, 546, 524, 544, 528, 546, 524, 1624, 526, 542, 528, 1622, 526, 544, 526, 546, 524, 1622, 526, 546, 526, 1622, 526, 544, 526, 546, 526, 544, 524, 546, 524, 546, 524, 546, 524, 1622, 526, 1620, 526, 546, 526, 1622, 524, 1624, 526, 546, 524, 548, 524, 546, 524, 546, 524, 548, 524, 548, 522, 548, 524, 1624, 524, 550, 522, 546, 524, 548, 522, 1626, 524, 548, 522, 546, 524, 546, 524, 550, 496, 570, 524, 550, 496, 1648, 500, 1648, 524, 1624, 498, 1650, 498, 1648, 500, 1650, 498, 574, 498, 574, 496 };  // UNKNOWN 2E2F1562
          Serial.println("Turning the temperature to 22");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 25) {
          uint16_t rawData[243] = { 8286, 4254, 476, 620, 452, 1672, 474, 1674, 500, 572, 474, 1674, 500, 572, 474, 1674, 474, 598, 474, 1674, 500, 1650, 498, 570, 476, 620, 476, 1646, 502, 1648, 500, 1650, 500, 574, 498, 572, 474, 598, 498, 570, 476, 596, 500, 572, 498, 572, 498, 572, 500, 572, 500, 572, 498, 574, 498, 572, 500, 572, 498, 572, 526, 570, 476, 568, 502, 570, 526, 1622, 526, 546, 500, 570, 500, 594, 476, 572, 524, 1624, 524, 546, 526, 548, 522, 570, 502, 544, 526, 546, 524, 544, 526, 546, 526, 570, 500, 546, 526, 546, 524, 546, 526, 544, 528, 544, 526, 546, 526, 568, 502, 544, 526, 546, 524, 544, 528, 546, 526, 546, 524, 546, 526, 544, 526, 544, 526, 544, 528, 568, 502, 546, 524, 546, 526, 544, 526, 1622, 526, 1622, 526, 546, 526, 546, 524, 544, 526, 546, 526, 544, 526, 570, 502, 544, 526, 1622, 526, 544, 526, 1622, 526, 546, 526, 546, 526, 1624, 524, 546, 526, 1622, 528, 546, 524, 568, 500, 548, 524, 544, 528, 546, 524, 1624, 524, 546, 526, 1622, 526, 1648, 502, 546, 524, 544, 524, 546, 524, 550, 522, 1624, 524, 548, 522, 550, 522, 548, 522, 548, 522, 1622, 528, 546, 524, 548, 522, 546, 522, 1626, 522, 548, 524, 548, 522, 548, 500, 572, 498, 574, 498, 574, 496, 1652, 498, 574, 496, 1652, 498, 574, 496, 572, 500, 574, 496, 1652, 472, 602, 494 };  // UNKNOWN E7200DE4
          Serial.println("Turning the temperature to 23");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 26) {
          uint16_t rawData[243] = { 8340, 4204, 502, 570, 500, 1650, 500, 1650, 524, 572, 476, 1650, 526, 546, 526, 1624, 526, 548, 500, 596, 476, 570, 526, 1622, 528, 546, 524, 1624, 526, 1624, 526, 1628, 522, 548, 524, 546, 524, 548, 524, 548, 524, 546, 526, 546, 524, 546, 526, 544, 526, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 526, 546, 524, 546, 526, 544, 526, 1624, 526, 544, 526, 546, 526, 544, 528, 546, 526, 546, 526, 544, 526, 548, 524, 546, 526, 546, 524, 546, 526, 548, 524, 546, 526, 546, 526, 548, 526, 546, 524, 548, 526, 546, 526, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 1626, 524, 1626, 524, 548, 524, 546, 524, 548, 524, 548, 524, 548, 524, 548, 522, 548, 524, 1626, 524, 546, 524, 1626, 522, 574, 474, 572, 524, 1626, 498, 574, 498, 1652, 498, 574, 498, 574, 498, 576, 494, 576, 494, 576, 498, 576, 496, 1652, 474, 600, 472, 602, 496, 574, 472, 1680, 472, 598, 472, 600, 474, 1676, 474, 600, 470, 600, 446, 626, 446, 624, 472, 1678, 448, 626, 470, 602, 446, 624, 448, 1704, 470, 600, 472, 602, 446, 624, 448, 624, 448, 624, 446, 624, 448, 1702, 446, 624, 472, 1680, 448, 1702, 448, 1702, 448, 1702, 472, 600, 448, 624, 472 };  // UNKNOWN B255EFBA
          Serial.println("Turning the temperature to 24 ");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 27) {
          uint16_t rawData[243] = { 8334, 4206, 500, 572, 524, 1624, 500, 1672, 476, 570, 500, 1674, 476, 572, 476, 1674, 524, 548, 524, 1624, 526, 546, 526, 1626, 498, 572, 522, 1624, 524, 1628, 522, 1624, 526, 548, 526, 548, 524, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 546, 526, 548, 524, 570, 502, 570, 500, 546, 526, 546, 526, 544, 528, 544, 526, 546, 526, 548, 524, 1622, 526, 548, 524, 546, 526, 546, 524, 548, 524, 1620, 526, 546, 526, 546, 526, 546, 524, 546, 526, 546, 524, 544, 526, 546, 524, 546, 526, 546, 524, 548, 524, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 522, 548, 524, 572, 498, 550, 522, 548, 524, 546, 524, 546, 526, 548, 524, 550, 522, 548, 524, 550, 498, 572, 522, 550, 498, 1650, 522, 1626, 500, 572, 498, 576, 496, 598, 474, 576, 496, 574, 498, 572, 498, 572, 498, 1650, 498, 574, 496, 1652, 498, 574, 496, 574, 496, 1650, 498, 572, 496, 1650, 498, 574, 472, 598, 472, 600, 470, 598, 498, 576, 472, 1674, 496, 1652, 496, 576, 470, 598, 472, 1676, 472, 1678, 470, 598, 470, 602, 470, 1676, 472, 598, 472, 600, 470, 600, 446, 624, 470, 1676, 472, 602, 444, 628, 444, 624, 446, 1702, 470, 600, 446, 626, 446, 624, 448, 624, 446, 626, 444, 624, 446, 624, 446, 624, 448, 624, 446, 624, 446, 624, 446, 624, 446, 1700, 448, 626, 446 };  // UNKNOWN FC85993E
          Serial.println("Turning the temperature to 25");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 28) {
          uint16_t rawData[243] = { 8310, 4226, 526, 546, 502, 1646, 502, 1648, 502, 570, 500, 1650, 500, 570, 502, 1648, 500, 572, 500, 572, 498, 1648, 500, 1648, 524, 550, 522, 1624, 502, 1646, 502, 1646, 526, 546, 526, 546, 500, 572, 524, 546, 500, 572, 524, 548, 524, 544, 526, 546, 526, 546, 526, 546, 524, 550, 522, 546, 524, 544, 526, 548, 524, 544, 526, 546, 524, 572, 500, 1624, 526, 546, 524, 548, 524, 544, 526, 544, 526, 1622, 526, 544, 526, 544, 526, 546, 526, 546, 526, 546, 526, 570, 502, 544, 526, 544, 526, 546, 526, 544, 526, 548, 524, 546, 524, 546, 526, 546, 526, 544, 526, 544, 526, 544, 528, 544, 526, 546, 526, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 548, 522, 546, 526, 1622, 526, 1624, 526, 544, 526, 546, 526, 544, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1622, 526, 546, 524, 1624, 524, 546, 524, 546, 526, 1648, 500, 548, 524, 1624, 526, 546, 524, 546, 526, 546, 524, 546, 524, 548, 522, 1624, 524, 1622, 528, 1624, 524, 548, 524, 548, 524, 546, 524, 546, 524, 550, 522, 546, 524, 1624, 524, 546, 524, 548, 522, 550, 522, 1626, 524, 548, 522, 548, 522, 550, 496, 1652, 522, 548, 498, 572, 498, 572, 498, 572, 498, 572, 472, 602, 496, 1650, 498, 1650, 496, 576, 496, 574, 496, 574, 472, 598, 568, 1578, 472, 600, 472 };  // MIRAGE
          Serial.println("Turning the temperature to 26");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 29) {
          uint16_t rawData[243] = { 8340, 4204, 472, 624, 500, 1628, 496, 1652, 472, 600, 526, 1650, 448, 600, 550, 1624, 448, 600, 524, 1626, 472, 1702, 450, 1678, 472, 598, 472, 1702, 448, 1676, 474, 1676, 472, 598, 524, 546, 550, 520, 474, 598, 472, 600, 470, 598, 472, 598, 548, 524, 470, 600, 472, 598, 472, 598, 520, 550, 472, 596, 474, 594, 476, 598, 474, 598, 470, 598, 498, 1650, 498, 572, 500, 572, 474, 598, 472, 596, 476, 1672, 500, 570, 500, 572, 498, 572, 474, 596, 524, 546, 474, 596, 526, 544, 502, 570, 526, 548, 498, 570, 526, 544, 526, 568, 502, 544, 524, 546, 524, 570, 502, 548, 524, 544, 526, 546, 526, 544, 526, 544, 526, 546, 524, 544, 526, 546, 524, 546, 526, 544, 526, 546, 524, 546, 524, 546, 524, 1624, 524, 1622, 526, 546, 524, 544, 524, 546, 526, 544, 528, 544, 524, 546, 524, 546, 524, 1624, 524, 546, 522, 1626, 524, 546, 524, 548, 524, 1626, 522, 550, 498, 1650, 500, 572, 522, 546, 498, 576, 494, 574, 496, 574, 496, 1650, 496, 574, 496, 1652, 496, 1652, 498, 1650, 498, 574, 472, 598, 472, 600, 472, 602, 468, 1676, 472, 600, 470, 600, 470, 600, 470, 1676, 472, 598, 470, 600, 446, 624, 446, 1700, 472, 600, 446, 624, 446, 624, 446, 624, 446, 624, 446, 626, 446, 1700, 448, 1700, 448, 622, 448, 1700, 446, 622, 448, 624, 446, 1700, 448, 624, 446 };  // UNKNOWN CD149EA8
          Serial.println("Turning the temperature to 27");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 30) {
          uint16_t rawData[243] = { 8312, 4230, 500, 572, 500, 1648, 526, 1624, 526, 546, 500, 1672, 502, 546, 524, 1626, 524, 548, 524, 548, 524, 548, 524, 548, 524, 1624, 524, 1624, 526, 1624, 526, 1624, 524, 546, 526, 548, 524, 570, 500, 546, 500, 570, 526, 546, 524, 546, 528, 546, 524, 544, 526, 546, 526, 546, 526, 544, 526, 544, 526, 544, 526, 546, 526, 546, 524, 548, 524, 1622, 526, 548, 524, 546, 526, 548, 524, 546, 524, 1622, 526, 546, 526, 546, 526, 546, 526, 546, 526, 552, 520, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 544, 528, 544, 526, 544, 528, 544, 526, 544, 526, 546, 528, 544, 526, 546, 526, 544, 526, 544, 528, 544, 528, 544, 526, 570, 500, 546, 526, 546, 526, 544, 528, 1624, 526, 1648, 504, 544, 526, 544, 528, 546, 524, 546, 526, 546, 526, 544, 528, 544, 526, 1622, 528, 546, 526, 1624, 526, 544, 526, 546, 526, 1624, 526, 546, 524, 1624, 528, 544, 528, 544, 526, 546, 526, 544, 526, 546, 526, 1622, 526, 546, 526, 1622, 526, 548, 524, 1624, 528, 1622, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 524, 548, 524, 548, 526, 1622, 526, 546, 526, 546, 526, 544, 526, 1624, 526, 548, 524, 546, 524, 548, 524, 548, 524, 548, 522, 548, 524, 548, 522, 1626, 524, 1626, 522, 548, 524, 546, 500, 574, 498, 1652, 498, 572, 498 };  // MIRAGE
          Serial.println("Turning the temperature to 28");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 31) {
          uint16_t rawData[243] = { 8260, 4252, 498, 574, 550, 1598, 474, 1674, 524, 548, 498, 1650, 498, 576, 522, 1648, 450, 600, 498, 1676, 448, 598, 474, 596, 474, 1700, 450, 1700, 450, 1674, 474, 1676, 472, 598, 472, 600, 470, 598, 472, 624, 448, 598, 472, 622, 448, 598, 472, 598, 472, 596, 474, 598, 474, 596, 474, 598, 498, 596, 450, 598, 476, 596, 474, 596, 474, 598, 474, 1696, 476, 572, 500, 570, 500, 570, 500, 572, 496, 1650, 500, 572, 498, 574, 526, 544, 500, 570, 526, 546, 500, 572, 498, 574, 498, 570, 526, 546, 526, 546, 524, 548, 524, 546, 524, 546, 524, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 524, 546, 524, 546, 526, 570, 502, 570, 500, 548, 524, 546, 526, 548, 524, 546, 526, 546, 524, 1622, 526, 1626, 526, 570, 502, 546, 526, 546, 526, 546, 526, 548, 524, 546, 526, 546, 526, 1622, 526, 546, 524, 1624, 526, 546, 526, 546, 526, 1624, 526, 570, 502, 1622, 526, 548, 522, 570, 502, 544, 528, 546, 524, 548, 522, 1626, 524, 546, 524, 1624, 524, 1622, 526, 548, 524, 548, 524, 546, 526, 548, 524, 1624, 524, 1624, 526, 546, 524, 546, 524, 572, 500, 1624, 524, 546, 524, 548, 524, 546, 524, 1624, 524, 546, 524, 548, 522, 548, 524, 548, 498, 574, 522, 548, 524, 1624, 524, 572, 498, 1626, 524, 1628, 522, 548, 498, 574, 522, 1626, 522, 546, 500 };  // UNKNOWN E0379B0
          Serial.println("Turning the temperature to 29");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 32) {
          uint16_t rawData[243] = { 8282, 4276, 450, 598, 474, 1700, 448, 1702, 448, 624, 450, 1674, 472, 598, 476, 1674, 474, 596, 474, 622, 474, 1650, 472, 598, 500, 1648, 474, 1676, 496, 1652, 470, 1700, 450, 598, 500, 594, 476, 570, 500, 570, 498, 574, 500, 570, 500, 570, 502, 568, 500, 570, 502, 570, 500, 570, 502, 570, 524, 548, 524, 546, 500, 570, 526, 548, 524, 546, 524, 1622, 526, 546, 524, 548, 522, 572, 500, 544, 526, 1620, 526, 570, 502, 546, 524, 544, 524, 570, 502, 544, 526, 544, 526, 544, 524, 546, 524, 544, 526, 572, 500, 546, 526, 544, 528, 544, 526, 544, 528, 544, 526, 544, 526, 546, 524, 546, 526, 544, 526, 568, 502, 546, 526, 544, 526, 546, 524, 546, 524, 544, 526, 548, 524, 546, 524, 546, 524, 1622, 526, 1620, 526, 548, 524, 544, 526, 546, 526, 546, 526, 546, 524, 546, 524, 544, 526, 1624, 526, 546, 524, 1624, 524, 546, 524, 548, 524, 1622, 526, 546, 524, 1624, 526, 546, 524, 548, 524, 546, 524, 548, 522, 546, 524, 1624, 524, 1624, 524, 1650, 500, 1624, 524, 1624, 524, 548, 524, 548, 522, 550, 522, 1624, 524, 1626, 522, 548, 498, 574, 498, 574, 496, 1652, 498, 572, 500, 576, 494, 574, 498, 1652, 498, 574, 498, 574, 496, 574, 496, 574, 470, 600, 470, 600, 472, 1676, 472, 600, 470, 624, 446, 600, 470, 1676, 470, 600, 472, 1678, 444, 624, 470 };  // UNKNOWN 98C1DCF6
          Serial.println("Turning the temperature to 30");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        }
        Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC ON at ", r, " & Cold V: ", cold, "/Hot V: ", hot);
        Serial.println("Turning the Auto - Ac ON");
        a++;
        thand = 0;
      } else {
        if (t < cold && thand == 0 && fanmode == 0) {
          uint16_t rawData[243] = { 8282, 4264, 464, 580, 492, 1684, 466, 1684, 466, 606, 464, 1684, 466, 580, 490, 1682, 466, 608, 468, 604, 466, 1656, 494, 606, 464, 606, 464, 1684, 464, 1684, 464, 1660, 492, 582, 490, 580, 492, 582, 490, 606, 464, 580, 492, 580, 492, 606, 464, 580, 490, 608, 466, 578, 492, 578, 492, 582, 490, 584, 486, 606, 466, 580, 490, 580, 492, 582, 490, 1658, 490, 582, 490, 606, 464, 606, 466, 606, 464, 1660, 492, 580, 492, 582, 492, 580, 490, 580, 492, 552, 520, 582, 490, 606, 464, 608, 464, 1656, 494, 1658, 492, 580, 492, 580, 492, 606, 466, 606, 466, 582, 490, 580, 490, 582, 490, 578, 494, 580, 490, 580, 492, 580, 492, 580, 492, 606, 466, 580, 492, 582, 490, 580, 492, 580, 492, 580, 492, 1684, 466, 1658, 492, 582, 492, 580, 490, 580, 492, 606, 466, 606, 464, 580, 492, 580, 490, 1656, 492, 582, 490, 1656, 492, 580, 490, 554, 520, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 580, 490, 580, 494, 580, 492, 578, 492, 1658, 492, 580, 492, 582, 490, 1686, 466, 1660, 490, 580, 492, 584, 488, 1660, 492, 580, 492, 580, 490, 580, 490, 578, 494, 580, 492, 606, 466, 580, 492, 1658, 492, 580, 492, 1660, 490, 1660, 492, 580, 490, 580, 492, 606, 466, 580, 492, 1658, 490, 580, 492, 580, 490, 580, 492, 1656, 494, 580, 492, 1684, 464, 580, 492 };  // MIRAGE
          irsend.sendRaw(rawData, 243, 38);
          Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC OFF at ", t, " & Cold V: ", cold, "/Hot V: ", hot);
          Serial.println("Turning the Auto - Ac off");
          thand = 1;
          a = 0;
          b = 0;
        } else if (t < cold && thand == 0 && fanmode == 1) {
          uint16_t rawData[243] = { 8304, 4238, 490, 582, 490, 1660, 490, 1686, 464, 608, 464, 1686, 464, 582, 492, 1658, 492, 582, 490, 608, 464, 582, 488, 1662, 490, 584, 488, 1686, 466, 1686, 464, 1688, 464, 582, 488, 582, 490, 608, 464, 582, 490, 608, 464, 610, 462, 582, 492, 606, 464, 608, 464, 608, 464, 608, 464, 608, 464, 580, 492, 584, 486, 582, 490, 582, 490, 582, 492, 1686, 464, 608, 464, 606, 464, 608, 464, 1662, 490, 582, 490, 1660, 490, 608, 462, 608, 464, 608, 464, 608, 464, 580, 490, 608, 464, 584, 486, 608, 464, 582, 490, 582, 490, 582, 490, 580, 492, 580, 490, 582, 490, 580, 490, 584, 488, 582, 490, 608, 464, 580, 492, 554, 518, 582, 490, 580, 490, 582, 490, 580, 492, 584, 488, 584, 488, 582, 490, 1660, 492, 1660, 492, 582, 492, 580, 492, 580, 492, 582, 490, 580, 490, 582, 490, 582, 488, 1658, 490, 582, 490, 1660, 490, 582, 490, 584, 490, 1686, 466, 582, 490, 1660, 490, 606, 464, 582, 490, 582, 492, 578, 492, 586, 488, 578, 492, 580, 492, 1684, 466, 608, 466, 1660, 490, 580, 492, 606, 466, 580, 492, 580, 492, 582, 492, 1656, 492, 580, 492, 1660, 490, 1660, 492, 582, 492, 580, 492, 582, 490, 582, 492, 1660, 492, 1660, 490, 580, 492, 582, 490, 582, 492, 582, 492, 1658, 492, 1658, 492, 1660, 492, 1662, 490, 580, 492, 582, 490, 1660, 490, 580, 490 };  // MIRAGE
          Serial.println("Mode 3 Fan");
          irsend.sendRaw(rawData, 243, 38);
          Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC FAN ON", t, " & Cold V: ", cold, "/Hot V: ", hot);
          thand = 1;
          a = 0;
          b = 0;
// ============================================================================
// OPTIMIZED SETUP AND LOOP FUNCTIONS
// ============================================================================

void setup()
{
  Serial.begin(115200);
  delay(100);

  Serial.println("=== AC Control System v3.0 ===");
  Serial.println("Dual IR LED Configuration");
  Serial.println("Optimized for Blynk IoT Platform");

  // Initialize Blynk connection
  BlynkEdgent.begin();

  // Initialize sensors and IR transmitters
  dht.begin();
  irsend1.begin();  // Initialize primary IR LED
  irsend2.begin();  // Initialize secondary IR LED

  // Initialize system state
  sysState.autoMode = false;
  sysState.fanMode = false;
  sysState.coldThreshold = 24.0;
  sysState.hotThreshold = 25.0;
  sysState.targetTemp = 18;
  sysState.exactTemp = 0.0;
  sysState.autoStage = 0;
  sysState.stabilityCounter = 0;
  sysState.tempHandled = false;
  sysState.initialized = false;

  // Set up optimized timer intervals
  timer.setInterval(3000L, SendSensor);      // Sensor reading every 3 seconds (reduced from 2s)
  timer.setInterval(5000L, AUTOTEMP);        // Auto temperature control every 5 seconds
  timer.setInterval(30000L, performMaintenance); // System maintenance every 30 seconds

  Serial.println("System initialized successfully");
  Serial.println("IR LED 1 (Primary): Pin D2 (GPIO 4)");
  Serial.println("IR LED 2 (Secondary): Pin D1 (GPIO 5)");
  Serial.println("DHT22 Sensor: Pin D6");
  Serial.println("Ready for operation...");
}

void loop() {
  BlynkEdgent.run();
  timer.run();

  // Watchdog - restart if system becomes unresponsive
  static unsigned long lastHeartbeat = 0;
  if (millis() - lastHeartbeat > 60000) { // 1 minute watchdog
    lastHeartbeat = millis();
    Serial.println("System heartbeat: " + String(millis()/1000) + "s uptime");
  }
}

// ============================================================================
// SYSTEM MAINTENANCE FUNCTION
// ============================================================================

void performMaintenance() {
  // Check WiFi connection
  if (!Blynk.connected()) {
    Serial.println("Blynk disconnected - attempting reconnection");
    return;
  }

  // Memory usage check
  Serial.println("Free heap: " + String(ESP.getFreeHeap()) + " bytes");

  // Validate sensor readings
  if (millis() - lastDHTRead > 10000) { // If no sensor reading for 10 seconds
    Serial.println("Sensor reading timeout - forcing refresh");
    readDHTSensor();
  }

  // Auto mode status check
  if (sysState.autoMode) {
    Serial.println("Auto AC Status: Stage=" + String(sysState.autoStage) +
                  ", Stability=" + String(sysState.stabilityCounter) +
                  ", Target=" + String(sysState.targetTemp) + "°C");
  }
}
