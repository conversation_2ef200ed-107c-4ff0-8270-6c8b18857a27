/*************************************************************
  Blynk is a platform with iOS and Android apps to control
  ESP32, Arduino, Raspberry Pi and the likes over the Internet.
  You can easily build mobile and web interfaces for any
  projects by simply dragging and dropping widgets.

    Downloads, docs, tutorials: https://www.blynk.io
    Sketch generator:           https://examples.blynk.cc
    Blynk community:            https://community.blynk.cc
    Follow us:                  https://www.fb.com/blynkapp
                                https://twitter.com/blynk_app

  Blynk library is licensed under MIT license
 *************************************************************
  Blynk.Edgent implements:
  - Blynk.Inject - Dynamic WiFi credentials provisioning
  - Blynk.Air    - Over The Air firmware updates
  - Device state indication using a physical LED
  - Credentials reset using a physical Button
 *************************************************************/

/* Fill in information from your Blynk Template here */
/* Read more: https://bit.ly/BlynkInject */

// Uncomment your board, or configure a custom board in Settings.h


#define BLYNK_TEMPLATE_ID "TMPLn1fK4fPE"
#define BLYNK_TEMPLATE_NAME "AC CONTROL"


#define BLYNK_FIRMWARE_VERSION "2.1"
#define BLYNK_PRINT Serial
#define APP_DEBUG

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include "DHT.h"  // what digital pin the DHT22 is conected to
#define DHTTYPE DHT22
#include "DHT.h"
#define DHTPIN D6

//#define USE_SPARKFUN_BLYNK_BOARD
#define USE_NODE_MCU_BOARD
//#define USE_WITTY_CLOUD_BOARD
//#define USE_WEMOS_D1_MINI

#include "BlynkEdgent.h"
//#define BLYNK_DEBUG


BlynkTimer timer;

int thand = 0;
int a = 0;
int b = 0;
int c = 0;
int d = 0;
int fanmode = 0;
const uint16_t kIrLed = 4;
IRsend irsend(kIrLed);
DHT dht(DHTPIN, DHTTYPE);
int AUTO = 0;
float cold = 24;
float hot = 25;
int r = 18;
float exacttemp = 0;


//Sensor.........................................................................................................................................................................................................................................................................................................................................................

void SendSensor() {
  if (d == 0) {
    Blynk.syncVirtual(V7);
    AUTOTEMP();
    d++;
  }

  float temp = dht.readTemperature();
  float hue = dht.readHumidity();

  if (isnan(hue) || isnan(temp)) {
    Serial.println("Failed to Read The Sensor DHT");
    return;
  }
  Serial.print("Temperature = ");
  Serial.print(temp);
  Serial.println(" C");
  Serial.print("Humidity = ");
  Serial.print(hue);
  Serial.println(" /kg");
  Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, " & AC OFF / AUTO AC OFF");
}
//Speaker........................................................................................................................................................................................................................................................................................................................................................

BLYNK_WRITE(V3)  //Speaker On/Off
{
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();
  if (param.asInt() == 2) {
    uint16_t rawData[71] = { 9250, 4538, 592, 1672, 592, 556, 592, 556, 592, 558, 588, 556, 590, 558, 588, 556, 592, 558, 588, 556, 590, 1672, 590, 1670, 592, 1672, 590, 1672, 588, 1672, 590, 1672, 590, 1672, 588, 558, 590, 1672, 588, 556, 590, 556, 588, 1674, 588, 556, 590, 554, 590, 558, 586, 1674, 588, 554, 590, 1674, 588, 1670, 590, 556, 588, 1672, 590, 1670, 590, 1674, 590, 40122, 9218, 2254, 588 };  // NEC 807F48B7

    Serial.println("Speaker BT");
    irsend.sendNEC(0x807FD827, 32);
    irsend.sendNEC(0x807FD827, 32);
  } else if (param.asInt() == 1) {
    uint16_t rawData[71] = { 9250, 4538, 592, 1672, 592, 556, 592, 556, 592, 558, 588, 556, 590, 558, 588, 556, 592, 558, 588, 556, 590, 1672, 590, 1670, 592, 1672, 590, 1672, 588, 1672, 590, 1672, 590, 1672, 588, 558, 590, 1672, 588, 556, 590, 556, 588, 1674, 588, 556, 590, 554, 590, 558, 586, 1674, 588, 554, 590, 1674, 588, 1670, 590, 556, 588, 1672, 590, 1670, 590, 1674, 590, 40122, 9218, 2254, 588 };  // NEC 807F48B7
    Serial.println("Speaker is On with BT");
    irsend.sendNEC(0x807F48B7, 32);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 0) {
    uint16_t rawData[71] = { 9250, 4538, 592, 1672, 592, 556, 592, 556, 592, 558, 588, 556, 590, 558, 588, 556, 592, 558, 588, 556, 590, 1672, 590, 1670, 592, 1672, 590, 1672, 588, 1672, 590, 1672, 590, 1672, 588, 558, 590, 1672, 588, 556, 590, 556, 588, 1674, 588, 556, 590, 554, 590, 558, 586, 1674, 588, 554, 590, 1674, 588, 1670, 590, 556, 588, 1672, 590, 1670, 590, 1674, 590, 40122, 9218, 2254, 588 };  // NEC 807F48B7
    Serial.println("Speaker Off");
    irsend.sendNEC(0x807F48B7, 32);
  } else if (param.asInt() == 3) {
    uint16_t rawData[243] = { 8308, 4264, 464, 606, 466, 1660, 490, 1686, 466, 584, 488, 1630, 518, 580, 492, 1658, 492, 582, 490, 578, 492, 1660, 490, 578, 494, 606, 464, 1660, 490, 1684, 466, 1658, 492, 582, 492, 580, 492, 554, 518, 580, 492, 606, 464, 582, 490, 580, 492, 580, 490, 608, 464, 606, 464, 606, 466, 578, 492, 1658, 490, 584, 488, 580, 490, 584, 490, 606, 464, 1658, 490, 580, 492, 580, 490, 606, 464, 580, 492, 1684, 464, 580, 492, 580, 492, 582, 488, 554, 516, 582, 490, 582, 490, 580, 490, 580, 492, 580, 492, 582, 490, 580, 492, 582, 490, 582, 490, 608, 464, 580, 492, 580, 492, 580, 490, 580, 492, 582, 490, 580, 490, 582, 490, 606, 464, 606, 464, 580, 492, 580, 492, 580, 492, 580, 492, 580, 492, 1686, 464, 1656, 492, 584, 490, 578, 492, 582, 488, 582, 490, 580, 492, 580, 492, 580, 492, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 1656, 492, 582, 490, 1656, 494, 578, 494, 580, 492, 582, 490, 582, 490, 582, 492, 1658, 492, 1658, 494, 578, 494, 578, 492, 1660, 492, 1658, 492, 580, 492, 582, 490, 580, 492, 1658, 492, 1658, 492, 582, 490, 1658, 492, 1656, 492, 580, 492, 580, 492, 578, 494, 580, 492, 1658, 492, 1660, 492, 580, 492, 578, 494, 580, 492, 578, 494, 1656, 492, 580, 492, 1658, 492, 578, 494, 1656, 494, 580, 494, 1654, 494, 578, 492 };  // MIRAGE
    Serial.println("Display OFF/ON");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  }

  else if (param.asInt() == 4) {
    uint16_t rawData[243] = { 8298, 4242, 490, 584, 516, 1634, 518, 1658, 464, 584, 514, 1636, 488, 584, 462, 1684, 464, 610, 488, 582, 490, 1660, 540, 532, 490, 582, 514, 1606, 570, 1608, 488, 1660, 490, 582, 514, 558, 488, 582, 542, 530, 490, 580, 516, 560, 460, 606, 490, 584, 486, 584, 488, 584, 514, 554, 488, 582, 516, 554, 488, 582, 488, 584, 512, 560, 488, 584, 516, 1636, 488, 584, 488, 584, 488, 584, 490, 582, 516, 1636, 486, 584, 516, 558, 510, 560, 514, 556, 516, 556, 488, 582, 490, 582, 514, 556, 488, 584, 490, 582, 490, 582, 490, 586, 512, 558, 488, 584, 514, 558, 488, 582, 490, 608, 464, 582, 490, 582, 490, 580, 516, 556, 464, 608, 488, 582, 516, 582, 464, 584, 486, 582, 490, 584, 488, 584, 488, 1660, 544, 1606, 516, 530, 542, 556, 488, 586, 462, 608, 488, 582, 490, 582, 488, 584, 490, 1660, 488, 580, 490, 1662, 486, 610, 462, 582, 490, 1660, 464, 606, 492, 1660, 464, 608, 514, 556, 464, 608, 514, 558, 486, 584, 490, 1660, 490, 1660, 488, 1660, 488, 584, 490, 582, 488, 584, 490, 580, 490, 582, 516, 1636, 488, 584, 486, 1662, 462, 608, 516, 1632, 488, 1660, 518, 556, 492, 582, 516, 556, 488, 584, 488, 1660, 518, 1634, 490, 582, 514, 556, 490, 582, 488, 584, 514, 1636, 516, 556, 488, 1662, 486, 1664, 488, 584, 490, 582, 488, 1662, 514, 556, 492 };  // MIRAGE
    Serial.println("Mode - 1 Cool");
    irsend.sendRaw(rawData, 243, 38);
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, " & Ac Status : Cool, ");
    Blynk.virtualWrite(V12, "Cool");
  } else if (param.asInt() == 5) {
    uint16_t rawData[243] = { 8396, 4180, 472, 598, 474, 1674, 474, 1676, 476, 598, 474, 1678, 472, 598, 528, 1626, 472, 598, 476, 1676, 474, 598, 474, 1702, 448, 1702, 448, 600, 526, 1626, 472, 1676, 476, 598, 474, 598, 474, 624, 500, 544, 528, 544, 528, 544, 528, 544, 528, 544, 528, 546, 526, 546, 528, 544, 526, 546, 552, 520, 528, 544, 552, 518, 528, 546, 550, 520, 552, 1598, 552, 492, 554, 570, 528, 520, 548, 520, 528, 1624, 472, 598, 526, 546, 550, 522, 526, 546, 524, 546, 528, 546, 524, 546, 552, 520, 526, 548, 550, 522, 548, 522, 524, 544, 554, 520, 500, 570, 552, 520, 474, 600, 522, 546, 500, 570, 528, 546, 524, 544, 526, 548, 524, 544, 502, 570, 528, 544, 502, 572, 526, 544, 528, 546, 524, 544, 528, 1622, 526, 1650, 500, 546, 526, 548, 526, 542, 530, 544, 526, 544, 528, 546, 526, 544, 526, 1622, 528, 544, 526, 1622, 528, 544, 528, 546, 528, 1624, 526, 546, 524, 1624, 526, 548, 524, 546, 526, 548, 524, 546, 526, 548, 522, 1626, 524, 1626, 524, 1624, 526, 548, 524, 548, 522, 1626, 526, 548, 524, 548, 498, 1650, 524, 546, 500, 572, 500, 574, 498, 1652, 524, 546, 498, 576, 496, 574, 496, 576, 472, 1676, 502, 572, 498, 574, 472, 598, 474, 600, 472, 600, 470, 600, 472, 1678, 474, 600, 472, 600, 470, 1678, 474, 600, 470, 600, 472, 1650, 498, 598, 474 };  // UNKNOWN E4CD5DDE
    Serial.println("Mode 2 Dry");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, " & Ac Status : DRY, ");
    Blynk.virtualWrite(V12, "Dry");
  } else if (param.asInt() == 6) {
    uint16_t rawData[243] = { 8304, 4238, 490, 582, 490, 1660, 490, 1686, 464, 608, 464, 1686, 464, 582, 492, 1658, 492, 582, 490, 608, 464, 582, 488, 1662, 490, 584, 488, 1686, 466, 1686, 464, 1688, 464, 582, 488, 582, 490, 608, 464, 582, 490, 608, 464, 610, 462, 582, 492, 606, 464, 608, 464, 608, 464, 608, 464, 608, 464, 580, 492, 584, 486, 582, 490, 582, 490, 582, 492, 1686, 464, 608, 464, 606, 464, 608, 464, 1662, 490, 582, 490, 1660, 490, 608, 462, 608, 464, 608, 464, 608, 464, 580, 490, 608, 464, 584, 486, 608, 464, 582, 490, 582, 490, 582, 490, 580, 492, 580, 490, 582, 490, 580, 490, 584, 488, 582, 490, 608, 464, 580, 492, 554, 518, 582, 490, 580, 490, 582, 490, 580, 492, 584, 488, 584, 488, 582, 490, 1660, 492, 1660, 492, 582, 492, 580, 492, 580, 492, 582, 490, 580, 490, 582, 490, 582, 488, 1658, 490, 582, 490, 1660, 490, 582, 490, 584, 490, 1686, 466, 582, 490, 1660, 490, 606, 464, 582, 490, 582, 492, 578, 492, 586, 488, 578, 492, 580, 492, 1684, 466, 608, 466, 1660, 490, 580, 492, 606, 466, 580, 492, 580, 492, 582, 492, 1656, 492, 580, 492, 1660, 490, 1660, 492, 582, 492, 580, 492, 582, 490, 582, 492, 1660, 492, 1660, 490, 580, 492, 582, 490, 582, 492, 582, 492, 1658, 492, 1658, 492, 1660, 492, 1662, 490, 580, 492, 582, 490, 1660, 490, 580, 490 };  // MIRAGE
    Serial.println("Mode 3 Fan");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, " & Ac Status : Fan, ");
    Blynk.virtualWrite(V12, "Fan");
  } else if (param.asInt() == 7) {
    uint16_t rawData[243] = { 8282, 4268, 464, 608, 464, 1660, 492, 1658, 494, 608, 466, 1662, 490, 584, 488, 1658, 492, 608, 464, 608, 464, 1660, 492, 582, 490, 582, 488, 1686, 464, 1684, 464, 1658, 492, 608, 464, 606, 466, 606, 466, 582, 490, 580, 492, 580, 492, 608, 464, 582, 490, 580, 492, 582, 490, 582, 490, 582, 490, 582, 490, 606, 464, 608, 464, 582, 490, 582, 490, 1686, 464, 582, 490, 580, 492, 606, 466, 582, 490, 1660, 490, 608, 464, 580, 494, 582, 490, 580, 492, 606, 464, 582, 492, 608, 464, 582, 488, 606, 464, 608, 464, 606, 466, 1684, 466, 582, 490, 580, 490, 582, 492, 606, 466, 578, 492, 582, 490, 606, 464, 608, 464, 582, 488, 608, 464, 580, 492, 582, 490, 606, 464, 584, 488, 582, 490, 580, 492, 1658, 492, 1686, 464, 608, 464, 608, 464, 582, 490, 582, 490, 608, 464, 582, 490, 580, 492, 1660, 490, 580, 492, 1684, 464, 580, 492, 580, 492, 1662, 488, 582, 490, 1658, 490, 582, 490, 580, 492, 580, 492, 582, 488, 580, 492, 578, 492, 580, 492, 1660, 490, 1686, 464, 580, 492, 582, 490, 580, 490, 580, 492, 580, 492, 580, 492, 578, 494, 1658, 490, 1660, 490, 1658, 492, 580, 492, 582, 490, 580, 490, 584, 490, 1684, 464, 1656, 494, 578, 492, 580, 492, 580, 492, 578, 494, 1656, 494, 1658, 490, 1660, 492, 578, 492, 1658, 492, 578, 494, 1658, 492, 580, 492 };  // MIRAGE
    Serial.println("Air Refresh On");
    irsend.sendRaw(rawData, 243, 38);
  } else if (param.asInt() == 8) {
    uint16_t rawData[243] = { 8304, 4238, 494, 552, 520, 1634, 518, 1658, 492, 580, 492, 1632, 518, 552, 520, 1632, 518, 554, 520, 580, 492, 1658, 492, 582, 492, 580, 492, 1658, 492, 1632, 520, 1632, 520, 554, 518, 580, 492, 556, 516, 554, 518, 554, 518, 554, 520, 578, 492, 580, 492, 556, 516, 554, 516, 554, 518, 580, 492, 580, 492, 554, 518, 580, 492, 524, 548, 556, 516, 1632, 518, 554, 518, 554, 518, 552, 518, 554, 518, 1632, 520, 580, 490, 556, 516, 554, 612, 462, 518, 554, 518, 580, 492, 552, 520, 552, 520, 554, 518, 582, 490, 552, 520, 580, 490, 556, 516, 556, 518, 580, 490, 554, 518, 582, 490, 554, 518, 582, 490, 580, 490, 582, 490, 582, 490, 580, 490, 582, 492, 582, 490, 556, 518, 582, 490, 556, 516, 1660, 492, 1662, 490, 554, 518, 556, 516, 584, 490, 556, 516, 584, 490, 582, 466, 582, 492, 1684, 490, 556, 514, 1634, 494, 608, 464, 606, 466, 1686, 464, 580, 492, 1684, 466, 582, 490, 582, 490, 582, 488, 608, 464, 582, 490, 1686, 466, 1684, 464, 1686, 464, 582, 490, 608, 464, 1684, 464, 608, 466, 580, 492, 582, 490, 606, 464, 608, 464, 1684, 466, 1660, 490, 1660, 490, 580, 490, 608, 464, 608, 464, 608, 464, 1686, 464, 1684, 464, 606, 466, 580, 492, 580, 492, 608, 464, 608, 464, 1684, 464, 582, 490, 580, 492, 1660, 490, 608, 464, 1660, 490, 606, 466 };  // MIRAGE
    Serial.println("Air Refresh Off");
    irsend.sendRaw(rawData, 243, 38);
    Blynk.virtualWrite(V14, 0);
  }
}

BLYNK_WRITE(V4)  //Volume Up
{
  if (param.asInt() == 0) {
    uint16_t rawData[77] = { 408, 656, 98, 706, 8300, 4662, 534, 1866, 470, 760, 460, 688, 536, 684, 536, 762, 428, 786, 466, 784, 436, 756, 466, 754, 466, 1870, 238, 168, 48, 1910, 360, 1904, 510, 1870, 402, 1860, 536, 1868, 348, 1920, 474, 818, 462, 1872, 390, 828, 470, 686, 502, 816, 438, 754, 434, 720, 534, 782, 438, 1870, 402, 818, 434, 1902, 464, 1868, 466, 1868, 466, 1898, 378, 1860, 476, 1858, 506, 40242, 10214, 2450, 468 };  // UNKNOWN A5E5B639
    Serial.println("Volume Up");
    irsend.sendNEC(0x807F40BF, 32);  // Send a raw data capture at 38kHz.
  }
}
BLYNK_WRITE(V5)  //Volume Down
{
  if (param.asInt() == 0) {
    Serial.println("Volume Down");
    irsend.sendNEC(0x807FC837, 32);  // Send a raw data capture at 38kHz.
  }
}

//AC Controls........................................................................................................................................................................................................................................................................................................................................................


BLYNK_WRITE(V2)  //AC OFF
{
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();
  if (param.asInt() == 1) {
    uint16_t rawData[243] = { 8282, 4264, 464, 580, 492, 1684, 466, 1684, 466, 606, 464, 1684, 466, 580, 490, 1682, 466, 608, 468, 604, 466, 1656, 494, 606, 464, 606, 464, 1684, 464, 1684, 464, 1660, 492, 582, 490, 580, 492, 582, 490, 606, 464, 580, 492, 580, 492, 606, 464, 580, 490, 608, 466, 578, 492, 578, 492, 582, 490, 584, 486, 606, 466, 580, 490, 580, 492, 582, 490, 1658, 490, 582, 490, 606, 464, 606, 466, 606, 464, 1660, 492, 580, 492, 582, 492, 580, 490, 580, 492, 552, 520, 582, 490, 606, 464, 608, 464, 1656, 494, 1658, 492, 580, 492, 580, 492, 606, 466, 606, 466, 582, 490, 580, 490, 582, 490, 578, 494, 580, 490, 580, 492, 580, 492, 580, 492, 606, 466, 580, 492, 582, 490, 580, 492, 580, 492, 580, 492, 1684, 466, 1658, 492, 582, 492, 580, 490, 580, 492, 606, 466, 606, 464, 580, 492, 580, 490, 1656, 492, 582, 490, 1656, 492, 580, 490, 554, 520, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 580, 490, 580, 494, 580, 492, 578, 492, 1658, 492, 580, 492, 582, 490, 1686, 466, 1660, 490, 580, 492, 584, 488, 1660, 492, 580, 492, 580, 490, 580, 490, 578, 494, 580, 492, 606, 466, 580, 492, 1658, 492, 580, 492, 1660, 490, 1660, 492, 580, 490, 580, 492, 606, 466, 580, 492, 1658, 490, 580, 492, 580, 490, 580, 492, 1656, 494, 580, 492, 1684, 464, 580, 492 };  // MIRAGE
    Serial.println("Turning the Ac off");
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Ac Status : OFF & Cold V: ", cold, "/Hot V: ", hot);
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  }
}

BLYNK_WRITE(V1)  //AC ON
{
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();

  if (param.asInt() == 1) {
    uint16_t rawData[243] = { 8312, 4264, 466, 606, 466, 1684, 468, 1658, 492, 578, 494, 1684, 466, 606, 464, 1684, 466, 606, 466, 606, 466, 1684, 468, 606, 466, 608, 466, 1660, 490, 1684, 466, 1658, 494, 608, 464, 606, 466, 608, 466, 606, 466, 580, 492, 580, 492, 606, 466, 580, 492, 608, 466, 606, 466, 582, 492, 606, 466, 606, 466, 606, 466, 606, 466, 580, 492, 606, 466, 1684, 466, 606, 464, 606, 466, 582, 490, 606, 466, 1682, 466, 606, 466, 606, 466, 606, 466, 606, 464, 580, 492, 606, 464, 606, 466, 580, 492, 606, 464, 608, 464, 582, 490, 606, 466, 606, 466, 580, 492, 604, 466, 606, 466, 606, 466, 580, 492, 550, 520, 606, 466, 606, 464, 606, 464, 606, 466, 580, 490, 606, 466, 608, 464, 606, 464, 606, 466, 1684, 464, 1684, 466, 606, 466, 606, 464, 580, 492, 608, 464, 608, 464, 606, 466, 604, 464, 1686, 464, 606, 464, 1684, 466, 606, 466, 606, 466, 1684, 466, 604, 464, 1658, 492, 578, 492, 604, 466, 606, 466, 606, 466, 606, 464, 1656, 492, 606, 464, 1684, 466, 1658, 490, 1658, 490, 582, 490, 606, 466, 582, 490, 1684, 466, 606, 466, 606, 464, 578, 494, 606, 464, 606, 466, 606, 466, 608, 464, 1684, 464, 580, 490, 1684, 466, 1684, 464, 606, 464, 606, 466, 606, 466, 608, 464, 580, 490, 1656, 492, 1684, 464, 1684, 466, 606, 466, 606, 466, 1656, 492, 582, 488 };  // MIRAGE
    Serial.println("Turning the Ac ON");
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Ac Status : ON & Cold V: ", cold, "/Hot V: ", hot);
    irsend.sendRaw(rawData, 243, 38);
  }
}




BLYNK_WRITE(V0)  //Temperature Control
{
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();
  if (param.asInt() == 16) {
    uint16_t rawData[243] = { 8314, 4224, 504, 570, 502, 1648, 502, 1646, 526, 544, 526, 1624, 500, 570, 500, 1648, 528, 546, 526, 546, 526, 546, 500, 1624, 526, 1648, 526, 548, 500, 1646, 526, 1624, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 544, 526, 520, 552, 544, 526, 546, 526, 546, 526, 546, 526, 544, 526, 544, 526, 544, 526, 546, 526, 546, 524, 546, 526, 1622, 526, 546, 524, 544, 526, 548, 522, 546, 524, 1626, 526, 544, 530, 544, 526, 544, 526, 544, 528, 544, 526, 570, 502, 546, 526, 544, 526, 544, 526, 544, 528, 544, 526, 542, 528, 544, 528, 544, 526, 544, 526, 546, 526, 542, 528, 544, 528, 544, 528, 546, 526, 546, 526, 544, 528, 544, 526, 544, 526, 546, 526, 544, 528, 544, 528, 546, 526, 1620, 526, 1622, 528, 546, 526, 544, 528, 544, 526, 544, 526, 544, 528, 544, 526, 570, 500, 1624, 526, 544, 526, 1622, 526, 546, 526, 546, 526, 1648, 502, 544, 528, 1624, 526, 544, 528, 544, 524, 546, 526, 546, 530, 544, 528, 1620, 528, 1624, 526, 544, 528, 1620, 528, 546, 526, 544, 528, 546, 528, 544, 528, 544, 528, 544, 526, 544, 526, 544, 526, 1622, 526, 546, 526, 544, 526, 544, 526, 544, 526, 1622, 524, 546, 526, 546, 526, 544, 526, 546, 524, 544, 528, 548, 524, 1622, 528, 546, 526, 546, 526, 1626, 524, 544, 526, 546, 524, 1624, 526, 546, 526 };  // MIRAGE
    Serial.println("Turning the tepmerature to 16");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 17) {
    uint16_t rawData[243] = { 8396, 4180, 472, 598, 474, 1674, 474, 1676, 476, 598, 474, 1678, 472, 598, 528, 1626, 472, 598, 476, 1676, 474, 598, 474, 1702, 448, 1702, 448, 600, 526, 1626, 472, 1676, 476, 598, 474, 598, 474, 624, 500, 544, 528, 544, 528, 544, 528, 544, 528, 544, 528, 546, 526, 546, 528, 544, 526, 546, 552, 520, 528, 544, 552, 518, 528, 546, 550, 520, 552, 1598, 552, 492, 554, 570, 528, 520, 548, 520, 528, 1624, 472, 598, 526, 546, 550, 522, 526, 546, 524, 546, 528, 546, 524, 546, 552, 520, 526, 548, 550, 522, 548, 522, 524, 544, 554, 520, 500, 570, 552, 520, 474, 600, 522, 546, 500, 570, 528, 546, 524, 544, 526, 548, 524, 544, 502, 570, 528, 544, 502, 572, 526, 544, 528, 546, 524, 544, 528, 1622, 526, 1650, 500, 546, 526, 548, 526, 542, 530, 544, 526, 544, 528, 546, 526, 544, 526, 1622, 528, 544, 526, 1622, 528, 544, 528, 546, 528, 1624, 526, 546, 524, 1624, 526, 548, 524, 546, 526, 548, 524, 546, 526, 548, 522, 1626, 524, 1626, 524, 1624, 526, 548, 524, 548, 522, 1626, 526, 548, 524, 548, 498, 1650, 524, 546, 500, 572, 500, 574, 498, 1652, 524, 546, 498, 576, 496, 574, 496, 576, 472, 1676, 502, 572, 498, 574, 472, 598, 474, 600, 472, 600, 470, 600, 472, 1678, 474, 600, 472, 600, 470, 1678, 474, 600, 470, 600, 472, 1650, 498, 598, 474 };  // UNKNOWN E4CD5DDE
    Serial.println("Turning the temperature to 17");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 18) {
    uint16_t rawData[243] = { 8340, 4206, 550, 522, 550, 1600, 548, 1600, 544, 530, 528, 1622, 552, 520, 552, 1594, 556, 520, 550, 518, 556, 1596, 578, 1572, 554, 1600, 548, 522, 528, 1622, 580, 1572, 528, 544, 528, 546, 550, 522, 524, 544, 550, 522, 522, 546, 526, 548, 546, 526, 516, 558, 522, 548, 524, 572, 500, 548, 522, 548, 522, 548, 522, 548, 522, 548, 524, 548, 498, 1650, 524, 548, 524, 548, 498, 572, 498, 574, 522, 1622, 528, 546, 526, 548, 496, 574, 520, 552, 496, 574, 498, 574, 498, 572, 498, 574, 498, 572, 472, 602, 494, 552, 494, 598, 472, 600, 472, 602, 466, 630, 446, 600, 458, 614, 470, 600, 472, 600, 470, 600, 472, 600, 472, 600, 472, 602, 444, 624, 444, 624, 452, 624, 448, 624, 448, 622, 450, 1700, 472, 1676, 472, 598, 474, 598, 448, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 1698, 448, 624, 450, 1700, 448, 624, 448, 624, 448, 1702, 446, 622, 450, 1702, 446, 624, 448, 622, 448, 622, 450, 622, 448, 624, 448, 1700, 448, 1728, 420, 1700, 450, 624, 446, 1704, 446, 1702, 448, 624, 448, 622, 474, 1676, 450, 1700, 448, 626, 446, 624, 472, 1678, 446, 624, 472, 596, 472, 600, 470, 600, 470, 1678, 446, 626, 472, 598, 472, 600, 470, 600, 472, 600, 472, 600, 470, 1680, 446, 624, 472, 1680, 444, 1704, 470, 602, 472, 598, 474, 1676, 474, 598, 474 };  // UNKNOWN 5E90CEB8
    Serial.println("Turning the temperature to 18");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 19) {
    uint16_t rawData[243] = { 8290, 4254, 526, 520, 550, 1624, 528, 1620, 528, 546, 502, 1648, 526, 570, 500, 1624, 526, 570, 476, 1646, 528, 1620, 528, 1622, 526, 1624, 524, 546, 526, 1622, 528, 1620, 526, 546, 526, 570, 502, 544, 528, 546, 524, 542, 528, 544, 526, 544, 526, 544, 528, 544, 528, 544, 526, 546, 526, 544, 528, 546, 524, 544, 526, 544, 526, 544, 526, 570, 502, 1620, 528, 542, 528, 544, 528, 546, 524, 544, 528, 1622, 528, 546, 524, 544, 528, 544, 528, 544, 526, 542, 528, 544, 528, 544, 526, 544, 528, 544, 528, 542, 528, 544, 528, 542, 528, 542, 528, 544, 526, 544, 528, 544, 526, 544, 528, 544, 528, 544, 526, 542, 528, 542, 528, 546, 526, 546, 526, 544, 528, 546, 528, 544, 526, 544, 528, 544, 526, 1622, 528, 1622, 528, 544, 526, 544, 528, 542, 528, 544, 526, 546, 526, 544, 528, 544, 528, 1620, 528, 544, 528, 1622, 528, 542, 528, 546, 526, 1620, 528, 546, 524, 1622, 528, 544, 528, 544, 528, 544, 528, 544, 526, 546, 528, 1622, 526, 544, 526, 544, 526, 544, 526, 1648, 500, 544, 526, 546, 526, 544, 524, 1622, 526, 544, 526, 1622, 526, 546, 526, 1624, 526, 544, 526, 544, 526, 546, 524, 546, 524, 1624, 524, 546, 526, 546, 524, 546, 524, 546, 524, 548, 500, 572, 522, 548, 500, 570, 500, 572, 498, 1650, 500, 572, 498, 574, 498, 1648, 500, 572, 498 };  // MIRAGE
    Serial.println("Turning the temperature to 19");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 20) {
    uint16_t rawData[243] = { 8336, 4196, 528, 548, 524, 1620, 528, 1622, 526, 546, 524, 1624, 526, 546, 526, 1622, 526, 544, 526, 548, 524, 544, 526, 544, 526, 546, 524, 1622, 528, 1596, 554, 1624, 526, 544, 528, 544, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 570, 502, 546, 526, 544, 526, 544, 528, 568, 502, 546, 526, 546, 526, 1622, 526, 544, 526, 544, 528, 544, 526, 544, 526, 1620, 528, 542, 528, 546, 526, 544, 526, 544, 528, 544, 526, 544, 528, 542, 530, 542, 530, 544, 528, 544, 528, 544, 526, 544, 528, 544, 528, 544, 528, 544, 528, 542, 528, 544, 526, 548, 524, 568, 504, 544, 526, 544, 528, 544, 526, 544, 526, 546, 526, 542, 528, 544, 528, 544, 526, 544, 528, 1622, 528, 1622, 526, 544, 528, 544, 528, 544, 528, 544, 528, 542, 528, 542, 528, 542, 528, 1620, 528, 544, 528, 1622, 526, 546, 526, 546, 528, 1620, 530, 542, 528, 1622, 528, 542, 528, 544, 526, 544, 528, 546, 524, 544, 526, 544, 526, 544, 528, 544, 528, 544, 526, 1622, 528, 1622, 528, 546, 526, 546, 524, 546, 526, 1622, 526, 546, 526, 546, 526, 1622, 526, 1624, 524, 548, 524, 546, 526, 1624, 526, 546, 524, 548, 500, 570, 526, 546, 500, 570, 500, 572, 500, 572, 500, 1646, 502, 570, 502, 572, 498, 1648, 500, 1648, 498, 1652, 498, 570, 500, 570, 500 };  // MIRAGE
    Serial.println("Turning the temperature to 20 ");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 21) {
    uint16_t rawData[243] = { 8292, 4252, 500, 572, 472, 1678, 472, 1678, 472, 598, 448, 1704, 470, 598, 450, 1700, 474, 600, 472, 1678, 472, 598, 448, 624, 448, 624, 446, 1700, 474, 1674, 474, 1678, 470, 600, 448, 624, 450, 624, 448, 622, 448, 622, 448, 624, 448, 622, 450, 628, 444, 624, 450, 624, 448, 622, 450, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 622, 450, 1702, 450, 626, 446, 622, 448, 622, 448, 624, 448, 1700, 450, 622, 450, 622, 450, 622, 450, 622, 448, 624, 448, 622, 448, 622, 448, 622, 450, 622, 450, 622, 450, 624, 448, 620, 450, 622, 450, 624, 448, 624, 472, 596, 474, 596, 474, 596, 450, 620, 450, 622, 474, 598, 472, 596, 474, 598, 450, 622, 450, 622, 472, 598, 476, 598, 472, 598, 474, 1674, 448, 1700, 450, 624, 448, 624, 472, 596, 450, 622, 474, 596, 472, 600, 472, 600, 472, 1672, 450, 624, 448, 1700, 448, 622, 474, 598, 474, 1672, 450, 624, 472, 1676, 472, 600, 472, 596, 474, 596, 474, 596, 474, 596, 476, 596, 474, 598, 474, 1674, 474, 598, 472, 1676, 474, 596, 476, 596, 474, 622, 450, 1674, 474, 1674, 474, 1674, 474, 1676, 472, 1674, 476, 596, 500, 572, 500, 572, 500, 570, 502, 1648, 474, 596, 500, 572, 500, 570, 500, 570, 500, 570, 502, 568, 502, 570, 502, 570, 500, 568, 504, 1646, 502, 570, 502, 568, 502, 1646, 502, 568, 504 };  // UNKNOWN 929340A4
    Serial.println("Turning the temperature to 21");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 22) {
    uint16_t rawData[243] = { 8282, 4254, 472, 598, 472, 1678, 472, 1676, 472, 598, 474, 1676, 472, 622, 448, 1678, 472, 600, 472, 624, 446, 1678, 472, 600, 472, 600, 470, 1678, 472, 1676, 474, 1700, 448, 598, 474, 598, 474, 598, 474, 622, 448, 598, 474, 596, 476, 622, 448, 598, 474, 598, 472, 596, 474, 622, 474, 574, 474, 598, 472, 598, 474, 596, 498, 572, 474, 598, 498, 1648, 498, 574, 474, 600, 496, 572, 500, 572, 500, 1648, 500, 574, 498, 570, 500, 570, 526, 570, 476, 572, 524, 546, 500, 570, 524, 572, 500, 546, 524, 548, 524, 548, 524, 546, 526, 546, 526, 546, 524, 544, 526, 546, 526, 544, 526, 570, 500, 546, 526, 568, 502, 544, 524, 546, 524, 546, 524, 544, 526, 544, 526, 544, 526, 542, 528, 546, 524, 1622, 526, 1622, 526, 544, 526, 546, 526, 568, 502, 544, 526, 546, 524, 544, 528, 546, 524, 1624, 526, 542, 528, 1622, 526, 544, 526, 546, 524, 1622, 526, 546, 526, 1622, 526, 544, 526, 546, 526, 544, 524, 546, 524, 546, 524, 546, 524, 1622, 526, 1620, 526, 546, 526, 1622, 524, 1624, 526, 546, 524, 548, 524, 546, 524, 546, 524, 548, 524, 548, 522, 548, 524, 1624, 524, 550, 522, 546, 524, 548, 522, 1626, 524, 548, 522, 546, 524, 546, 524, 550, 496, 570, 524, 550, 496, 1648, 500, 1648, 524, 1624, 498, 1650, 498, 1648, 500, 1650, 498, 574, 498, 574, 496 };  // UNKNOWN 2E2F1562
    Serial.println("Turning the temperature to 22");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 23) {
    uint16_t rawData[243] = { 8286, 4254, 476, 620, 452, 1672, 474, 1674, 500, 572, 474, 1674, 500, 572, 474, 1674, 474, 598, 474, 1674, 500, 1650, 498, 570, 476, 620, 476, 1646, 502, 1648, 500, 1650, 500, 574, 498, 572, 474, 598, 498, 570, 476, 596, 500, 572, 498, 572, 498, 572, 500, 572, 500, 572, 498, 574, 498, 572, 500, 572, 498, 572, 526, 570, 476, 568, 502, 570, 526, 1622, 526, 546, 500, 570, 500, 594, 476, 572, 524, 1624, 524, 546, 526, 548, 522, 570, 502, 544, 526, 546, 524, 544, 526, 546, 526, 570, 500, 546, 526, 546, 524, 546, 526, 544, 528, 544, 526, 546, 526, 568, 502, 544, 526, 546, 524, 544, 528, 546, 526, 546, 524, 546, 526, 544, 526, 544, 526, 544, 528, 568, 502, 546, 524, 546, 526, 544, 526, 1622, 526, 1622, 526, 546, 526, 546, 524, 544, 526, 546, 526, 544, 526, 570, 502, 544, 526, 1622, 526, 544, 526, 1622, 526, 546, 526, 546, 526, 1624, 524, 546, 526, 1622, 528, 546, 524, 568, 500, 548, 524, 544, 528, 546, 524, 1624, 524, 546, 526, 1622, 526, 1648, 502, 546, 524, 544, 524, 546, 524, 550, 522, 1624, 524, 548, 522, 550, 522, 548, 522, 548, 522, 1622, 528, 546, 524, 548, 522, 546, 522, 1626, 522, 548, 524, 548, 522, 548, 500, 572, 498, 574, 498, 574, 496, 1652, 498, 574, 496, 1652, 498, 574, 496, 572, 500, 574, 496, 1652, 472, 602, 494 };  // UNKNOWN E7200DE4
    Serial.println("Turning the temperature to 23");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 24) {
    uint16_t rawData[243] = { 8340, 4204, 502, 570, 500, 1650, 500, 1650, 524, 572, 476, 1650, 526, 546, 526, 1624, 526, 548, 500, 596, 476, 570, 526, 1622, 528, 546, 524, 1624, 526, 1624, 526, 1628, 522, 548, 524, 546, 524, 548, 524, 548, 524, 546, 526, 546, 524, 546, 526, 544, 526, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 526, 546, 524, 546, 526, 544, 526, 1624, 526, 544, 526, 546, 526, 544, 528, 546, 526, 546, 526, 544, 526, 548, 524, 546, 526, 546, 524, 546, 526, 548, 524, 546, 526, 546, 526, 548, 526, 546, 524, 548, 526, 546, 526, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 1626, 524, 1626, 524, 548, 524, 546, 524, 548, 524, 548, 524, 548, 524, 548, 522, 548, 524, 1626, 524, 546, 524, 1626, 522, 574, 474, 572, 524, 1626, 498, 574, 498, 1652, 498, 574, 498, 574, 498, 576, 494, 576, 494, 576, 498, 576, 496, 1652, 474, 600, 472, 602, 496, 574, 472, 1680, 472, 598, 472, 600, 474, 1676, 474, 600, 470, 600, 446, 626, 446, 624, 472, 1678, 448, 626, 470, 602, 446, 624, 448, 1704, 470, 600, 472, 602, 446, 624, 448, 624, 448, 624, 446, 624, 448, 1702, 446, 624, 472, 1680, 448, 1702, 448, 1702, 448, 1702, 472, 600, 448, 624, 472 };  // UNKNOWN B255EFBA
    Serial.println("Turning the temperature to 24 ");
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Ac Status : ON & Cold V: ", cold, "/Hot V: ", hot);
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 25) {
    uint16_t rawData[243] = { 8334, 4206, 500, 572, 524, 1624, 500, 1672, 476, 570, 500, 1674, 476, 572, 476, 1674, 524, 548, 524, 1624, 526, 546, 526, 1626, 498, 572, 522, 1624, 524, 1628, 522, 1624, 526, 548, 526, 548, 524, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 546, 526, 548, 524, 570, 502, 570, 500, 546, 526, 546, 526, 544, 528, 544, 526, 546, 526, 548, 524, 1622, 526, 548, 524, 546, 526, 546, 524, 548, 524, 1620, 526, 546, 526, 546, 526, 546, 524, 546, 526, 546, 524, 544, 526, 546, 524, 546, 526, 546, 524, 548, 524, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 522, 548, 524, 572, 498, 550, 522, 548, 524, 546, 524, 546, 526, 548, 524, 550, 522, 548, 524, 550, 498, 572, 522, 550, 498, 1650, 522, 1626, 500, 572, 498, 576, 496, 598, 474, 576, 496, 574, 498, 572, 498, 572, 498, 1650, 498, 574, 496, 1652, 498, 574, 496, 574, 496, 1650, 498, 572, 496, 1650, 498, 574, 472, 598, 472, 600, 470, 598, 498, 576, 472, 1674, 496, 1652, 496, 576, 470, 598, 472, 1676, 472, 1678, 470, 598, 470, 602, 470, 1676, 472, 598, 472, 600, 470, 600, 446, 624, 470, 1676, 472, 602, 444, 628, 444, 624, 446, 1702, 470, 600, 446, 626, 446, 624, 448, 624, 446, 626, 444, 624, 446, 624, 446, 624, 448, 624, 446, 624, 446, 624, 446, 624, 446, 1700, 448, 626, 446 };  // UNKNOWN FC85993E
    Serial.println("Turning the temperature to 25");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 26) {
    uint16_t rawData[243] = { 8310, 4226, 526, 546, 502, 1646, 502, 1648, 502, 570, 500, 1650, 500, 570, 502, 1648, 500, 572, 500, 572, 498, 1648, 500, 1648, 524, 550, 522, 1624, 502, 1646, 502, 1646, 526, 546, 526, 546, 500, 572, 524, 546, 500, 572, 524, 548, 524, 544, 526, 546, 526, 546, 526, 546, 524, 550, 522, 546, 524, 544, 526, 548, 524, 544, 526, 546, 524, 572, 500, 1624, 526, 546, 524, 548, 524, 544, 526, 544, 526, 1622, 526, 544, 526, 544, 526, 546, 526, 546, 526, 546, 526, 570, 502, 544, 526, 544, 526, 546, 526, 544, 526, 548, 524, 546, 524, 546, 526, 546, 526, 544, 526, 544, 526, 544, 528, 544, 526, 546, 526, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 548, 522, 546, 526, 1622, 526, 1624, 526, 544, 526, 546, 526, 544, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1622, 526, 546, 524, 1624, 524, 546, 524, 546, 526, 1648, 500, 548, 524, 1624, 526, 546, 524, 546, 526, 546, 524, 546, 524, 548, 522, 1624, 524, 1622, 528, 1624, 524, 548, 524, 548, 524, 546, 524, 546, 524, 550, 522, 546, 524, 1624, 524, 546, 524, 548, 522, 550, 522, 1626, 524, 548, 522, 548, 522, 550, 496, 1652, 522, 548, 498, 572, 498, 572, 498, 572, 498, 572, 472, 602, 496, 1650, 498, 1650, 496, 576, 496, 574, 496, 574, 472, 598, 568, 1578, 472, 600, 472 };  // MIRAGE
    Serial.println("Turning the temperature to 26");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 27) {
    uint16_t rawData[243] = { 8340, 4204, 472, 624, 500, 1628, 496, 1652, 472, 600, 526, 1650, 448, 600, 550, 1624, 448, 600, 524, 1626, 472, 1702, 450, 1678, 472, 598, 472, 1702, 448, 1676, 474, 1676, 472, 598, 524, 546, 550, 520, 474, 598, 472, 600, 470, 598, 472, 598, 548, 524, 470, 600, 472, 598, 472, 598, 520, 550, 472, 596, 474, 594, 476, 598, 474, 598, 470, 598, 498, 1650, 498, 572, 500, 572, 474, 598, 472, 596, 476, 1672, 500, 570, 500, 572, 498, 572, 474, 596, 524, 546, 474, 596, 526, 544, 502, 570, 526, 548, 498, 570, 526, 544, 526, 568, 502, 544, 524, 546, 524, 570, 502, 548, 524, 544, 526, 546, 526, 544, 526, 544, 526, 546, 524, 544, 526, 546, 524, 546, 526, 544, 526, 546, 524, 546, 524, 546, 524, 1624, 524, 1622, 526, 546, 524, 544, 524, 546, 526, 544, 528, 544, 524, 546, 524, 546, 524, 1624, 524, 546, 522, 1626, 524, 546, 524, 548, 524, 1626, 522, 550, 498, 1650, 500, 572, 522, 546, 498, 576, 494, 574, 496, 574, 496, 1650, 496, 574, 496, 1652, 496, 1652, 498, 1650, 498, 574, 472, 598, 472, 600, 472, 602, 468, 1676, 472, 600, 470, 600, 470, 600, 470, 1676, 472, 598, 470, 600, 446, 624, 446, 1700, 472, 600, 446, 624, 446, 624, 446, 624, 446, 624, 446, 626, 446, 1700, 448, 1700, 448, 622, 448, 1700, 446, 622, 448, 624, 446, 1700, 448, 624, 446 };  // UNKNOWN CD149EA8
    Serial.println("Turning the temperature to 27");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 28) {
    uint16_t rawData[243] = { 8312, 4230, 500, 572, 500, 1648, 526, 1624, 526, 546, 500, 1672, 502, 546, 524, 1626, 524, 548, 524, 548, 524, 548, 524, 548, 524, 1624, 524, 1624, 526, 1624, 526, 1624, 524, 546, 526, 548, 524, 570, 500, 546, 500, 570, 526, 546, 524, 546, 528, 546, 524, 544, 526, 546, 526, 546, 526, 544, 526, 544, 526, 544, 526, 546, 526, 546, 524, 548, 524, 1622, 526, 548, 524, 546, 526, 548, 524, 546, 524, 1622, 526, 546, 526, 546, 526, 546, 526, 546, 526, 552, 520, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 544, 528, 544, 526, 544, 528, 544, 526, 544, 526, 546, 528, 544, 526, 546, 526, 544, 526, 544, 528, 544, 528, 544, 526, 570, 500, 546, 526, 546, 526, 544, 528, 1624, 526, 1648, 504, 544, 526, 544, 528, 546, 524, 546, 526, 546, 526, 544, 528, 544, 526, 1622, 528, 546, 526, 1624, 526, 544, 526, 546, 526, 1624, 526, 546, 524, 1624, 528, 544, 528, 544, 526, 546, 526, 544, 526, 546, 526, 1622, 526, 546, 526, 1622, 526, 548, 524, 1624, 528, 1622, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 524, 548, 524, 548, 526, 1622, 526, 546, 526, 546, 526, 544, 526, 1624, 526, 548, 524, 546, 524, 548, 524, 548, 524, 548, 522, 548, 524, 548, 522, 1626, 524, 1626, 522, 548, 524, 546, 500, 574, 498, 1652, 498, 572, 498 };  // MIRAGE
    Serial.println("Turning the temperature to 28");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 29) {
    uint16_t rawData[243] = { 8260, 4252, 498, 574, 550, 1598, 474, 1674, 524, 548, 498, 1650, 498, 576, 522, 1648, 450, 600, 498, 1676, 448, 598, 474, 596, 474, 1700, 450, 1700, 450, 1674, 474, 1676, 472, 598, 472, 600, 470, 598, 472, 624, 448, 598, 472, 622, 448, 598, 472, 598, 472, 596, 474, 598, 474, 596, 474, 598, 498, 596, 450, 598, 476, 596, 474, 596, 474, 598, 474, 1696, 476, 572, 500, 570, 500, 570, 500, 572, 496, 1650, 500, 572, 498, 574, 526, 544, 500, 570, 526, 546, 500, 572, 498, 574, 498, 570, 526, 546, 526, 546, 524, 548, 524, 546, 524, 546, 524, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 524, 546, 524, 546, 526, 570, 502, 570, 500, 548, 524, 546, 526, 548, 524, 546, 526, 546, 524, 1622, 526, 1626, 526, 570, 502, 546, 526, 546, 526, 546, 526, 548, 524, 546, 526, 546, 526, 1622, 526, 546, 524, 1624, 526, 546, 526, 546, 526, 1624, 526, 570, 502, 1622, 526, 548, 522, 570, 502, 544, 528, 546, 524, 548, 522, 1626, 524, 546, 524, 1624, 524, 1622, 526, 548, 524, 548, 524, 546, 526, 548, 524, 1624, 524, 1624, 526, 546, 524, 546, 524, 572, 500, 1624, 524, 546, 524, 548, 524, 546, 524, 1624, 524, 546, 524, 548, 522, 548, 524, 548, 498, 574, 522, 548, 524, 1624, 524, 572, 498, 1626, 524, 1628, 522, 548, 498, 574, 522, 1626, 522, 546, 500 };  // UNKNOWN E0379B0
    Serial.println("Turning the temperature to 29");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  } else if (param.asInt() == 30) {
    uint16_t rawData[243] = { 8282, 4276, 450, 598, 474, 1700, 448, 1702, 448, 624, 450, 1674, 472, 598, 476, 1674, 474, 596, 474, 622, 474, 1650, 472, 598, 500, 1648, 474, 1676, 496, 1652, 470, 1700, 450, 598, 500, 594, 476, 570, 500, 570, 498, 574, 500, 570, 500, 570, 502, 568, 500, 570, 502, 570, 500, 570, 502, 570, 524, 548, 524, 546, 500, 570, 526, 548, 524, 546, 524, 1622, 526, 546, 524, 548, 522, 572, 500, 544, 526, 1620, 526, 570, 502, 546, 524, 544, 524, 570, 502, 544, 526, 544, 526, 544, 524, 546, 524, 544, 526, 572, 500, 546, 526, 544, 528, 544, 526, 544, 528, 544, 526, 544, 526, 546, 524, 546, 526, 544, 526, 568, 502, 546, 526, 544, 526, 546, 524, 546, 524, 544, 526, 548, 524, 546, 524, 546, 524, 1622, 526, 1620, 526, 548, 524, 544, 526, 546, 526, 546, 526, 546, 524, 546, 524, 544, 526, 1624, 526, 546, 524, 1624, 524, 546, 524, 548, 524, 1622, 526, 546, 524, 1624, 526, 546, 524, 548, 524, 546, 524, 548, 522, 546, 524, 1624, 524, 1624, 524, 1650, 500, 1624, 524, 1624, 524, 548, 524, 548, 522, 550, 522, 1624, 524, 1626, 522, 548, 498, 574, 498, 574, 496, 1652, 498, 572, 500, 576, 494, 574, 498, 1652, 498, 574, 498, 574, 496, 574, 496, 574, 470, 600, 470, 600, 472, 1676, 472, 600, 470, 624, 446, 600, 470, 1676, 470, 600, 472, 1678, 444, 624, 470 };  // UNKNOWN 98C1DCF6
    Serial.println("Turning the temperature to 30");
    irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
  }
}

//Automation................................................................................................................................................................................................................................................................................


//Auto Ac ON

BLYNK_WRITE(V7) {
  if (c == 0) {
    Blynk.syncVirtual(V8);
    Blynk.syncVirtual(V9);
    c++;
  }
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();
  float status = param.asInt();
  if (status == 1) {
    AUTO = 1;
    fanmode = 0;
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Auto AC - On & ", "Cold V: ", cold, "/Hot V: ", hot);
    AUTOTEMP();
  } else if (status == 0) {
    AUTO = 0;
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Auto Ac is OFF & ", "Cold V:", cold, "Hot V:", hot);
    a = 0;
    b = 0;
    thand = 0;
    fanmode = 0;
  } else if (status == 2) {
    AUTO = 1;
    fanmode = 1;
    Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Auto AC - On & ", "Cold V: ", cold, "/Hot V: ", hot);
    AUTOTEMP();
  }
}

BLYNK_WRITE(V8)  //Auto AC HOT
{
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();
  int new_v8_value = param.asInt();

  // Ensure V8 is greater than V9 by at least 1
  if (new_v8_value <= cold) {
    new_v8_value = cold + 1;
  }
  hot = new_v8_value;
  Blynk.virtualWrite(V8, hot);  // Update V8 value if needed
  thand = 0;
  a = 0;
  b = 0;
  Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Ac Status : ON & Cold V: ", cold, "/Hot V: ", hot);
}


BLYNK_WRITE(V9)  //Auto AC COLD
{
  float temp = dht.readTemperature();
  float hue = dht.readHumidity();
  float new_v9_value = param.asInt();

  // Ensure V9 is less than V8 by at least 1
  if (new_v9_value >= hot) {
    new_v9_value = hot - 1;
  }
  cold = new_v9_value;
  Blynk.virtualWrite(V9, cold);
  thand = 0;
  a = 0;
  b = 0;
  Blynk.virtualWrite(V6, "TEMP : ", temp, "/", hue, "- Ac Status : ON & Cold V: ", cold, "/Hot V: ", hot);
}



void AUTOTEMP() {
  if (AUTO == 1) {
    float t = dht.readTemperature();
    float h = dht.readHumidity();
    if (a < 3) {
      if ((t >= hot) || (t > cold && t < hot)) {
        if (cold <= 18) {
          uint16_t rawData[243] = { 8314, 4224, 504, 570, 502, 1648, 502, 1646, 526, 544, 526, 1624, 500, 570, 500, 1648, 528, 546, 526, 546, 526, 546, 500, 1624, 526, 1648, 526, 548, 500, 1646, 526, 1624, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 544, 526, 520, 552, 544, 526, 546, 526, 546, 526, 546, 526, 544, 526, 544, 526, 544, 526, 546, 526, 546, 524, 546, 526, 1622, 526, 546, 524, 544, 526, 548, 522, 546, 524, 1626, 526, 544, 530, 544, 526, 544, 526, 544, 528, 544, 526, 570, 502, 546, 526, 544, 526, 544, 526, 544, 528, 544, 526, 542, 528, 544, 528, 544, 526, 544, 526, 546, 526, 542, 528, 544, 528, 544, 528, 546, 526, 546, 526, 544, 528, 544, 526, 544, 526, 546, 526, 544, 528, 544, 528, 546, 526, 1620, 526, 1622, 528, 546, 526, 544, 528, 544, 526, 544, 526, 544, 528, 544, 526, 570, 500, 1624, 526, 544, 526, 1622, 526, 546, 526, 546, 526, 1648, 502, 544, 528, 1624, 526, 544, 528, 544, 524, 546, 526, 546, 530, 544, 528, 1620, 528, 1624, 526, 544, 528, 1620, 528, 546, 526, 544, 528, 546, 528, 544, 528, 544, 528, 544, 526, 544, 526, 544, 526, 1622, 526, 546, 526, 544, 526, 544, 526, 544, 526, 1622, 524, 546, 526, 546, 526, 544, 526, 546, 524, 544, 528, 548, 524, 1622, 528, 546, 526, 546, 526, 1626, 524, 544, 526, 546, 524, 1624, 526, 546, 526 };  // MIRAGE
          Serial.println("Turning the temperature to 16");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 19) {
          uint16_t rawData[243] = { 8396, 4180, 472, 598, 474, 1674, 474, 1676, 476, 598, 474, 1678, 472, 598, 528, 1626, 472, 598, 476, 1676, 474, 598, 474, 1702, 448, 1702, 448, 600, 526, 1626, 472, 1676, 476, 598, 474, 598, 474, 624, 500, 544, 528, 544, 528, 544, 528, 544, 528, 544, 528, 546, 526, 546, 528, 544, 526, 546, 552, 520, 528, 544, 552, 518, 528, 546, 550, 520, 552, 1598, 552, 492, 554, 570, 528, 520, 548, 520, 528, 1624, 472, 598, 526, 546, 550, 522, 526, 546, 524, 546, 528, 546, 524, 546, 552, 520, 526, 548, 550, 522, 548, 522, 524, 544, 554, 520, 500, 570, 552, 520, 474, 600, 522, 546, 500, 570, 528, 546, 524, 544, 526, 548, 524, 544, 502, 570, 528, 544, 502, 572, 526, 544, 528, 546, 524, 544, 528, 1622, 526, 1650, 500, 546, 526, 548, 526, 542, 530, 544, 526, 544, 528, 546, 526, 544, 526, 1622, 528, 544, 526, 1622, 528, 544, 528, 546, 528, 1624, 526, 546, 524, 1624, 526, 548, 524, 546, 526, 548, 524, 546, 526, 548, 522, 1626, 524, 1626, 524, 1624, 526, 548, 524, 548, 522, 1626, 526, 548, 524, 548, 498, 1650, 524, 546, 500, 572, 500, 574, 498, 1652, 524, 546, 498, 576, 496, 574, 496, 576, 472, 1676, 502, 572, 498, 574, 472, 598, 474, 600, 472, 600, 470, 600, 472, 1678, 474, 600, 472, 600, 470, 1678, 474, 600, 470, 600, 472, 1650, 498, 598, 474 };  // UNKNOWN E4CD5DDE
          Serial.println("Turning the temperature to 17");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 20) {
          uint16_t rawData[243] = { 8340, 4206, 550, 522, 550, 1600, 548, 1600, 544, 530, 528, 1622, 552, 520, 552, 1594, 556, 520, 550, 518, 556, 1596, 578, 1572, 554, 1600, 548, 522, 528, 1622, 580, 1572, 528, 544, 528, 546, 550, 522, 524, 544, 550, 522, 522, 546, 526, 548, 546, 526, 516, 558, 522, 548, 524, 572, 500, 548, 522, 548, 522, 548, 522, 548, 522, 548, 524, 548, 498, 1650, 524, 548, 524, 548, 498, 572, 498, 574, 522, 1622, 528, 546, 526, 548, 496, 574, 520, 552, 496, 574, 498, 574, 498, 572, 498, 574, 498, 572, 472, 602, 494, 552, 494, 598, 472, 600, 472, 602, 466, 630, 446, 600, 458, 614, 470, 600, 472, 600, 470, 600, 472, 600, 472, 600, 472, 602, 444, 624, 444, 624, 452, 624, 448, 624, 448, 622, 450, 1700, 472, 1676, 472, 598, 474, 598, 448, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 1698, 448, 624, 450, 1700, 448, 624, 448, 624, 448, 1702, 446, 622, 450, 1702, 446, 624, 448, 622, 448, 622, 450, 622, 448, 624, 448, 1700, 448, 1728, 420, 1700, 450, 624, 446, 1704, 446, 1702, 448, 624, 448, 622, 474, 1676, 450, 1700, 448, 626, 446, 624, 472, 1678, 446, 624, 472, 596, 472, 600, 470, 600, 470, 1678, 446, 626, 472, 598, 472, 600, 470, 600, 472, 600, 472, 600, 470, 1680, 446, 624, 472, 1680, 444, 1704, 470, 602, 472, 598, 474, 1676, 474, 598, 474 };  // UNKNOWN 5E90CEB8
          Serial.println("Turning the temperature to 18");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 21) {
          uint16_t rawData[243] = { 8290, 4254, 526, 520, 550, 1624, 528, 1620, 528, 546, 502, 1648, 526, 570, 500, 1624, 526, 570, 476, 1646, 528, 1620, 528, 1622, 526, 1624, 524, 546, 526, 1622, 528, 1620, 526, 546, 526, 570, 502, 544, 528, 546, 524, 542, 528, 544, 526, 544, 526, 544, 528, 544, 528, 544, 526, 546, 526, 544, 528, 546, 524, 544, 526, 544, 526, 544, 526, 570, 502, 1620, 528, 542, 528, 544, 528, 546, 524, 544, 528, 1622, 528, 546, 524, 544, 528, 544, 528, 544, 526, 542, 528, 544, 528, 544, 526, 544, 528, 544, 528, 542, 528, 544, 528, 542, 528, 542, 528, 544, 526, 544, 528, 544, 526, 544, 528, 544, 528, 544, 526, 542, 528, 542, 528, 546, 526, 546, 526, 544, 528, 546, 528, 544, 526, 544, 528, 544, 526, 1622, 528, 1622, 528, 544, 526, 544, 528, 542, 528, 544, 526, 546, 526, 544, 528, 544, 528, 1620, 528, 544, 528, 1622, 528, 542, 528, 546, 526, 1620, 528, 546, 524, 1622, 528, 544, 528, 544, 528, 544, 528, 544, 526, 546, 528, 1622, 526, 544, 526, 544, 526, 544, 526, 1648, 500, 544, 526, 546, 526, 544, 524, 1622, 526, 544, 526, 1622, 526, 546, 526, 1624, 526, 544, 526, 544, 526, 546, 524, 546, 524, 1624, 524, 546, 526, 546, 524, 546, 524, 546, 524, 548, 500, 572, 522, 548, 500, 570, 500, 572, 498, 1650, 500, 572, 498, 574, 498, 1648, 500, 572, 498 };  // MIRAGE
          Serial.println("Turning the temperature to 19");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 22) {
          uint16_t rawData[243] = { 8336, 4196, 528, 548, 524, 1620, 528, 1622, 526, 546, 524, 1624, 526, 546, 526, 1622, 526, 544, 526, 548, 524, 544, 526, 544, 526, 546, 524, 1622, 528, 1596, 554, 1624, 526, 544, 528, 544, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 570, 502, 546, 526, 544, 526, 544, 528, 568, 502, 546, 526, 546, 526, 1622, 526, 544, 526, 544, 528, 544, 526, 544, 526, 1620, 528, 542, 528, 546, 526, 544, 526, 544, 528, 544, 526, 544, 528, 542, 530, 542, 530, 544, 528, 544, 528, 544, 526, 544, 528, 544, 528, 544, 528, 544, 528, 542, 528, 544, 526, 548, 524, 568, 504, 544, 526, 544, 528, 544, 526, 544, 526, 546, 526, 542, 528, 544, 528, 544, 526, 544, 528, 1622, 528, 1622, 526, 544, 528, 544, 528, 544, 528, 544, 528, 542, 528, 542, 528, 542, 528, 1620, 528, 544, 528, 1622, 526, 546, 526, 546, 528, 1620, 530, 542, 528, 1622, 528, 542, 528, 544, 526, 544, 528, 546, 524, 544, 526, 544, 526, 544, 528, 544, 528, 544, 526, 1622, 528, 1622, 528, 546, 526, 546, 524, 546, 526, 1622, 526, 546, 526, 546, 526, 1622, 526, 1624, 524, 548, 524, 546, 526, 1624, 526, 546, 524, 548, 500, 570, 526, 546, 500, 570, 500, 572, 500, 572, 500, 1646, 502, 570, 502, 572, 498, 1648, 500, 1648, 498, 1652, 498, 570, 500, 570, 500 };  // MIRAGE
          Serial.println("Turning the temperature to 20 ");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 23) {
          uint16_t rawData[243] = { 8292, 4252, 500, 572, 472, 1678, 472, 1678, 472, 598, 448, 1704, 470, 598, 450, 1700, 474, 600, 472, 1678, 472, 598, 448, 624, 448, 624, 446, 1700, 474, 1674, 474, 1678, 470, 600, 448, 624, 450, 624, 448, 622, 448, 622, 448, 624, 448, 622, 450, 628, 444, 624, 450, 624, 448, 622, 450, 622, 448, 624, 448, 624, 448, 622, 448, 624, 448, 622, 450, 1702, 450, 626, 446, 622, 448, 622, 448, 624, 448, 1700, 450, 622, 450, 622, 450, 622, 450, 622, 448, 624, 448, 622, 448, 622, 448, 622, 450, 622, 450, 622, 450, 624, 448, 620, 450, 622, 450, 624, 448, 624, 472, 596, 474, 596, 474, 596, 450, 620, 450, 622, 474, 598, 472, 596, 474, 598, 450, 622, 450, 622, 472, 598, 476, 598, 472, 598, 474, 1674, 448, 1700, 450, 624, 448, 624, 472, 596, 450, 622, 474, 596, 472, 600, 472, 600, 472, 1672, 450, 624, 448, 1700, 448, 622, 474, 598, 474, 1672, 450, 624, 472, 1676, 472, 600, 472, 596, 474, 596, 474, 596, 474, 596, 476, 596, 474, 598, 474, 1674, 474, 598, 472, 1676, 474, 596, 476, 596, 474, 622, 450, 1674, 474, 1674, 474, 1674, 474, 1676, 472, 1674, 476, 596, 500, 572, 500, 572, 500, 570, 502, 1648, 474, 596, 500, 572, 500, 570, 500, 570, 500, 570, 502, 568, 502, 570, 502, 570, 500, 568, 504, 1646, 502, 570, 502, 568, 502, 1646, 502, 568, 504 };  // UNKNOWN 929340A4
          Serial.println("Turning the temperature to 21");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 24) {
          uint16_t rawData[243] = { 8282, 4254, 472, 598, 472, 1678, 472, 1676, 472, 598, 474, 1676, 472, 622, 448, 1678, 472, 600, 472, 624, 446, 1678, 472, 600, 472, 600, 470, 1678, 472, 1676, 474, 1700, 448, 598, 474, 598, 474, 598, 474, 622, 448, 598, 474, 596, 476, 622, 448, 598, 474, 598, 472, 596, 474, 622, 474, 574, 474, 598, 472, 598, 474, 596, 498, 572, 474, 598, 498, 1648, 498, 574, 474, 600, 496, 572, 500, 572, 500, 1648, 500, 574, 498, 570, 500, 570, 526, 570, 476, 572, 524, 546, 500, 570, 524, 572, 500, 546, 524, 548, 524, 548, 524, 546, 526, 546, 526, 546, 524, 544, 526, 546, 526, 544, 526, 570, 500, 546, 526, 568, 502, 544, 524, 546, 524, 546, 524, 544, 526, 544, 526, 544, 526, 542, 528, 546, 524, 1622, 526, 1622, 526, 544, 526, 546, 526, 568, 502, 544, 526, 546, 524, 544, 528, 546, 524, 1624, 526, 542, 528, 1622, 526, 544, 526, 546, 524, 1622, 526, 546, 526, 1622, 526, 544, 526, 546, 526, 544, 524, 546, 524, 546, 524, 546, 524, 1622, 526, 1620, 526, 546, 526, 1622, 524, 1624, 526, 546, 524, 548, 524, 546, 524, 546, 524, 548, 524, 548, 522, 548, 524, 1624, 524, 550, 522, 546, 524, 548, 522, 1626, 524, 548, 522, 546, 524, 546, 524, 550, 496, 570, 524, 550, 496, 1648, 500, 1648, 524, 1624, 498, 1650, 498, 1648, 500, 1650, 498, 574, 498, 574, 496 };  // UNKNOWN 2E2F1562
          Serial.println("Turning the temperature to 22");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 25) {
          uint16_t rawData[243] = { 8286, 4254, 476, 620, 452, 1672, 474, 1674, 500, 572, 474, 1674, 500, 572, 474, 1674, 474, 598, 474, 1674, 500, 1650, 498, 570, 476, 620, 476, 1646, 502, 1648, 500, 1650, 500, 574, 498, 572, 474, 598, 498, 570, 476, 596, 500, 572, 498, 572, 498, 572, 500, 572, 500, 572, 498, 574, 498, 572, 500, 572, 498, 572, 526, 570, 476, 568, 502, 570, 526, 1622, 526, 546, 500, 570, 500, 594, 476, 572, 524, 1624, 524, 546, 526, 548, 522, 570, 502, 544, 526, 546, 524, 544, 526, 546, 526, 570, 500, 546, 526, 546, 524, 546, 526, 544, 528, 544, 526, 546, 526, 568, 502, 544, 526, 546, 524, 544, 528, 546, 526, 546, 524, 546, 526, 544, 526, 544, 526, 544, 528, 568, 502, 546, 524, 546, 526, 544, 526, 1622, 526, 1622, 526, 546, 526, 546, 524, 544, 526, 546, 526, 544, 526, 570, 502, 544, 526, 1622, 526, 544, 526, 1622, 526, 546, 526, 546, 526, 1624, 524, 546, 526, 1622, 528, 546, 524, 568, 500, 548, 524, 544, 528, 546, 524, 1624, 524, 546, 526, 1622, 526, 1648, 502, 546, 524, 544, 524, 546, 524, 550, 522, 1624, 524, 548, 522, 550, 522, 548, 522, 548, 522, 1622, 528, 546, 524, 548, 522, 546, 522, 1626, 522, 548, 524, 548, 522, 548, 500, 572, 498, 574, 498, 574, 496, 1652, 498, 574, 496, 1652, 498, 574, 496, 572, 500, 574, 496, 1652, 472, 602, 494 };  // UNKNOWN E7200DE4
          Serial.println("Turning the temperature to 23");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 26) {
          uint16_t rawData[243] = { 8340, 4204, 502, 570, 500, 1650, 500, 1650, 524, 572, 476, 1650, 526, 546, 526, 1624, 526, 548, 500, 596, 476, 570, 526, 1622, 528, 546, 524, 1624, 526, 1624, 526, 1628, 522, 548, 524, 546, 524, 548, 524, 548, 524, 546, 526, 546, 524, 546, 526, 544, 526, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 526, 546, 524, 546, 526, 544, 526, 1624, 526, 544, 526, 546, 526, 544, 528, 546, 526, 546, 526, 544, 526, 548, 524, 546, 526, 546, 524, 546, 526, 548, 524, 546, 526, 546, 526, 548, 526, 546, 524, 548, 526, 546, 526, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 1626, 524, 1626, 524, 548, 524, 546, 524, 548, 524, 548, 524, 548, 524, 548, 522, 548, 524, 1626, 524, 546, 524, 1626, 522, 574, 474, 572, 524, 1626, 498, 574, 498, 1652, 498, 574, 498, 574, 498, 576, 494, 576, 494, 576, 498, 576, 496, 1652, 474, 600, 472, 602, 496, 574, 472, 1680, 472, 598, 472, 600, 474, 1676, 474, 600, 470, 600, 446, 626, 446, 624, 472, 1678, 448, 626, 470, 602, 446, 624, 448, 1704, 470, 600, 472, 602, 446, 624, 448, 624, 448, 624, 446, 624, 448, 1702, 446, 624, 472, 1680, 448, 1702, 448, 1702, 448, 1702, 472, 600, 448, 624, 472 };  // UNKNOWN B255EFBA
          Serial.println("Turning the temperature to 24 ");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 27) {
          uint16_t rawData[243] = { 8334, 4206, 500, 572, 524, 1624, 500, 1672, 476, 570, 500, 1674, 476, 572, 476, 1674, 524, 548, 524, 1624, 526, 546, 526, 1626, 498, 572, 522, 1624, 524, 1628, 522, 1624, 526, 548, 526, 548, 524, 546, 526, 546, 526, 544, 526, 546, 526, 546, 526, 546, 526, 548, 524, 570, 502, 570, 500, 546, 526, 546, 526, 544, 528, 544, 526, 546, 526, 548, 524, 1622, 526, 548, 524, 546, 526, 546, 524, 548, 524, 1620, 526, 546, 526, 546, 526, 546, 524, 546, 526, 546, 524, 544, 526, 546, 524, 546, 526, 546, 524, 548, 524, 546, 526, 546, 524, 548, 524, 548, 524, 546, 524, 548, 522, 548, 524, 572, 498, 550, 522, 548, 524, 546, 524, 546, 526, 548, 524, 550, 522, 548, 524, 550, 498, 572, 522, 550, 498, 1650, 522, 1626, 500, 572, 498, 576, 496, 598, 474, 576, 496, 574, 498, 572, 498, 572, 498, 1650, 498, 574, 496, 1652, 498, 574, 496, 574, 496, 1650, 498, 572, 496, 1650, 498, 574, 472, 598, 472, 600, 470, 598, 498, 576, 472, 1674, 496, 1652, 496, 576, 470, 598, 472, 1676, 472, 1678, 470, 598, 470, 602, 470, 1676, 472, 598, 472, 600, 470, 600, 446, 624, 470, 1676, 472, 602, 444, 628, 444, 624, 446, 1702, 470, 600, 446, 626, 446, 624, 448, 624, 446, 626, 444, 624, 446, 624, 446, 624, 448, 624, 446, 624, 446, 624, 446, 624, 446, 1700, 448, 626, 446 };  // UNKNOWN FC85993E
          Serial.println("Turning the temperature to 25");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 28) {
          uint16_t rawData[243] = { 8310, 4226, 526, 546, 502, 1646, 502, 1648, 502, 570, 500, 1650, 500, 570, 502, 1648, 500, 572, 500, 572, 498, 1648, 500, 1648, 524, 550, 522, 1624, 502, 1646, 502, 1646, 526, 546, 526, 546, 500, 572, 524, 546, 500, 572, 524, 548, 524, 544, 526, 546, 526, 546, 526, 546, 524, 550, 522, 546, 524, 544, 526, 548, 524, 544, 526, 546, 524, 572, 500, 1624, 526, 546, 524, 548, 524, 544, 526, 544, 526, 1622, 526, 544, 526, 544, 526, 546, 526, 546, 526, 546, 526, 570, 502, 544, 526, 544, 526, 546, 526, 544, 526, 548, 524, 546, 524, 546, 526, 546, 526, 544, 526, 544, 526, 544, 528, 544, 526, 546, 526, 546, 524, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 546, 526, 548, 522, 546, 526, 1622, 526, 1624, 526, 544, 526, 546, 526, 544, 526, 546, 526, 546, 526, 546, 526, 546, 526, 1622, 526, 546, 524, 1624, 524, 546, 524, 546, 526, 1648, 500, 548, 524, 1624, 526, 546, 524, 546, 526, 546, 524, 546, 524, 548, 522, 1624, 524, 1622, 528, 1624, 524, 548, 524, 548, 524, 546, 524, 546, 524, 550, 522, 546, 524, 1624, 524, 546, 524, 548, 522, 550, 522, 1626, 524, 548, 522, 548, 522, 550, 496, 1652, 522, 548, 498, 572, 498, 572, 498, 572, 498, 572, 472, 602, 496, 1650, 498, 1650, 496, 576, 496, 574, 496, 574, 472, 598, 568, 1578, 472, 600, 472 };  // MIRAGE
          Serial.println("Turning the temperature to 26");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 29) {
          uint16_t rawData[243] = { 8340, 4204, 472, 624, 500, 1628, 496, 1652, 472, 600, 526, 1650, 448, 600, 550, 1624, 448, 600, 524, 1626, 472, 1702, 450, 1678, 472, 598, 472, 1702, 448, 1676, 474, 1676, 472, 598, 524, 546, 550, 520, 474, 598, 472, 600, 470, 598, 472, 598, 548, 524, 470, 600, 472, 598, 472, 598, 520, 550, 472, 596, 474, 594, 476, 598, 474, 598, 470, 598, 498, 1650, 498, 572, 500, 572, 474, 598, 472, 596, 476, 1672, 500, 570, 500, 572, 498, 572, 474, 596, 524, 546, 474, 596, 526, 544, 502, 570, 526, 548, 498, 570, 526, 544, 526, 568, 502, 544, 524, 546, 524, 570, 502, 548, 524, 544, 526, 546, 526, 544, 526, 544, 526, 546, 524, 544, 526, 546, 524, 546, 526, 544, 526, 546, 524, 546, 524, 546, 524, 1624, 524, 1622, 526, 546, 524, 544, 524, 546, 526, 544, 528, 544, 524, 546, 524, 546, 524, 1624, 524, 546, 522, 1626, 524, 546, 524, 548, 524, 1626, 522, 550, 498, 1650, 500, 572, 522, 546, 498, 576, 494, 574, 496, 574, 496, 1650, 496, 574, 496, 1652, 496, 1652, 498, 1650, 498, 574, 472, 598, 472, 600, 472, 602, 468, 1676, 472, 600, 470, 600, 470, 600, 470, 1676, 472, 598, 470, 600, 446, 624, 446, 1700, 472, 600, 446, 624, 446, 624, 446, 624, 446, 624, 446, 626, 446, 1700, 448, 1700, 448, 622, 448, 1700, 446, 622, 448, 624, 446, 1700, 448, 624, 446 };  // UNKNOWN CD149EA8
          Serial.println("Turning the temperature to 27");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 30) {
          uint16_t rawData[243] = { 8312, 4230, 500, 572, 500, 1648, 526, 1624, 526, 546, 500, 1672, 502, 546, 524, 1626, 524, 548, 524, 548, 524, 548, 524, 548, 524, 1624, 524, 1624, 526, 1624, 526, 1624, 524, 546, 526, 548, 524, 570, 500, 546, 500, 570, 526, 546, 524, 546, 528, 546, 524, 544, 526, 546, 526, 546, 526, 544, 526, 544, 526, 544, 526, 546, 526, 546, 524, 548, 524, 1622, 526, 548, 524, 546, 526, 548, 524, 546, 524, 1622, 526, 546, 526, 546, 526, 546, 526, 546, 526, 552, 520, 546, 524, 548, 524, 546, 526, 546, 526, 546, 526, 546, 524, 546, 526, 544, 528, 544, 526, 544, 528, 544, 526, 544, 526, 546, 528, 544, 526, 546, 526, 544, 526, 544, 528, 544, 528, 544, 526, 570, 500, 546, 526, 546, 526, 544, 528, 1624, 526, 1648, 504, 544, 526, 544, 528, 546, 524, 546, 526, 546, 526, 544, 528, 544, 526, 1622, 528, 546, 526, 1624, 526, 544, 526, 546, 526, 1624, 526, 546, 524, 1624, 528, 544, 528, 544, 526, 546, 526, 544, 526, 546, 526, 1622, 526, 546, 526, 1622, 526, 548, 524, 1624, 528, 1622, 526, 546, 526, 546, 526, 546, 526, 1624, 526, 546, 524, 548, 524, 548, 526, 1622, 526, 546, 526, 546, 526, 544, 526, 1624, 526, 548, 524, 546, 524, 548, 524, 548, 524, 548, 522, 548, 524, 548, 522, 1626, 524, 1626, 522, 548, 524, 546, 500, 574, 498, 1652, 498, 572, 498 };  // MIRAGE
          Serial.println("Turning the temperature to 28");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 31) {
          uint16_t rawData[243] = { 8260, 4252, 498, 574, 550, 1598, 474, 1674, 524, 548, 498, 1650, 498, 576, 522, 1648, 450, 600, 498, 1676, 448, 598, 474, 596, 474, 1700, 450, 1700, 450, 1674, 474, 1676, 472, 598, 472, 600, 470, 598, 472, 624, 448, 598, 472, 622, 448, 598, 472, 598, 472, 596, 474, 598, 474, 596, 474, 598, 498, 596, 450, 598, 476, 596, 474, 596, 474, 598, 474, 1696, 476, 572, 500, 570, 500, 570, 500, 572, 496, 1650, 500, 572, 498, 574, 526, 544, 500, 570, 526, 546, 500, 572, 498, 574, 498, 570, 526, 546, 526, 546, 524, 548, 524, 546, 524, 546, 524, 546, 526, 546, 524, 546, 524, 546, 526, 546, 526, 546, 524, 546, 524, 546, 526, 570, 502, 570, 500, 548, 524, 546, 526, 548, 524, 546, 526, 546, 524, 1622, 526, 1626, 526, 570, 502, 546, 526, 546, 526, 546, 526, 548, 524, 546, 526, 546, 526, 1622, 526, 546, 524, 1624, 526, 546, 526, 546, 526, 1624, 526, 570, 502, 1622, 526, 548, 522, 570, 502, 544, 528, 546, 524, 548, 522, 1626, 524, 546, 524, 1624, 524, 1622, 526, 548, 524, 548, 524, 546, 526, 548, 524, 1624, 524, 1624, 526, 546, 524, 546, 524, 572, 500, 1624, 524, 546, 524, 548, 524, 546, 524, 1624, 524, 546, 524, 548, 522, 548, 524, 548, 498, 574, 522, 548, 524, 1624, 524, 572, 498, 1626, 524, 1628, 522, 548, 498, 574, 522, 1626, 522, 546, 500 };  // UNKNOWN E0379B0
          Serial.println("Turning the temperature to 29");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        } else if (cold <= 32) {
          uint16_t rawData[243] = { 8282, 4276, 450, 598, 474, 1700, 448, 1702, 448, 624, 450, 1674, 472, 598, 476, 1674, 474, 596, 474, 622, 474, 1650, 472, 598, 500, 1648, 474, 1676, 496, 1652, 470, 1700, 450, 598, 500, 594, 476, 570, 500, 570, 498, 574, 500, 570, 500, 570, 502, 568, 500, 570, 502, 570, 500, 570, 502, 570, 524, 548, 524, 546, 500, 570, 526, 548, 524, 546, 524, 1622, 526, 546, 524, 548, 522, 572, 500, 544, 526, 1620, 526, 570, 502, 546, 524, 544, 524, 570, 502, 544, 526, 544, 526, 544, 524, 546, 524, 544, 526, 572, 500, 546, 526, 544, 528, 544, 526, 544, 528, 544, 526, 544, 526, 546, 524, 546, 526, 544, 526, 568, 502, 546, 526, 544, 526, 546, 524, 546, 524, 544, 526, 548, 524, 546, 524, 546, 524, 1622, 526, 1620, 526, 548, 524, 544, 526, 546, 526, 546, 526, 546, 524, 546, 524, 544, 526, 1624, 526, 546, 524, 1624, 524, 546, 524, 548, 524, 1622, 526, 546, 524, 1624, 526, 546, 524, 548, 524, 546, 524, 548, 522, 546, 524, 1624, 524, 1624, 524, 1650, 500, 1624, 524, 1624, 524, 548, 524, 548, 522, 550, 522, 1624, 524, 1626, 522, 548, 498, 574, 498, 574, 496, 1652, 498, 572, 500, 576, 494, 574, 498, 1652, 498, 574, 498, 574, 496, 574, 496, 574, 470, 600, 470, 600, 472, 1676, 472, 600, 470, 624, 446, 600, 470, 1676, 470, 600, 472, 1678, 444, 624, 470 };  // UNKNOWN 98C1DCF6
          Serial.println("Turning the temperature to 30");
          irsend.sendRaw(rawData, 243, 38);  // Send a raw data capture at 38kHz.
        }
        Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC ON at ", r, " & Cold V: ", cold, "/Hot V: ", hot);
        Serial.println("Turning the Auto - Ac ON");
        a++;
        thand = 0;
      } else {
        if (t < cold && thand == 0 && fanmode == 0) {
          uint16_t rawData[243] = { 8282, 4264, 464, 580, 492, 1684, 466, 1684, 466, 606, 464, 1684, 466, 580, 490, 1682, 466, 608, 468, 604, 466, 1656, 494, 606, 464, 606, 464, 1684, 464, 1684, 464, 1660, 492, 582, 490, 580, 492, 582, 490, 606, 464, 580, 492, 580, 492, 606, 464, 580, 490, 608, 466, 578, 492, 578, 492, 582, 490, 584, 486, 606, 466, 580, 490, 580, 492, 582, 490, 1658, 490, 582, 490, 606, 464, 606, 466, 606, 464, 1660, 492, 580, 492, 582, 492, 580, 490, 580, 492, 552, 520, 582, 490, 606, 464, 608, 464, 1656, 494, 1658, 492, 580, 492, 580, 492, 606, 466, 606, 466, 582, 490, 580, 490, 582, 490, 578, 494, 580, 490, 580, 492, 580, 492, 580, 492, 606, 466, 580, 492, 582, 490, 580, 492, 580, 492, 580, 492, 1684, 466, 1658, 492, 582, 492, 580, 490, 580, 492, 606, 466, 606, 464, 580, 492, 580, 490, 1656, 492, 582, 490, 1656, 492, 580, 490, 554, 520, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 580, 490, 580, 494, 580, 492, 578, 492, 1658, 492, 580, 492, 582, 490, 1686, 466, 1660, 490, 580, 492, 584, 488, 1660, 492, 580, 492, 580, 490, 580, 490, 578, 494, 580, 492, 606, 466, 580, 492, 1658, 492, 580, 492, 1660, 490, 1660, 492, 580, 490, 580, 492, 606, 466, 580, 492, 1658, 490, 580, 492, 580, 490, 580, 492, 1656, 494, 580, 492, 1684, 464, 580, 492 };  // MIRAGE
          irsend.sendRaw(rawData, 243, 38);
          Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC OFF at ", t, " & Cold V: ", cold, "/Hot V: ", hot);
          Serial.println("Turning the Auto - Ac off");
          thand = 1;
          a = 0;
          b = 0;
        } else if (t < cold && thand == 0 && fanmode == 1) {
          uint16_t rawData[243] = { 8304, 4238, 490, 582, 490, 1660, 490, 1686, 464, 608, 464, 1686, 464, 582, 492, 1658, 492, 582, 490, 608, 464, 582, 488, 1662, 490, 584, 488, 1686, 466, 1686, 464, 1688, 464, 582, 488, 582, 490, 608, 464, 582, 490, 608, 464, 610, 462, 582, 492, 606, 464, 608, 464, 608, 464, 608, 464, 608, 464, 580, 492, 584, 486, 582, 490, 582, 490, 582, 492, 1686, 464, 608, 464, 606, 464, 608, 464, 1662, 490, 582, 490, 1660, 490, 608, 462, 608, 464, 608, 464, 608, 464, 580, 490, 608, 464, 584, 486, 608, 464, 582, 490, 582, 490, 582, 490, 580, 492, 580, 490, 582, 490, 580, 490, 584, 488, 582, 490, 608, 464, 580, 492, 554, 518, 582, 490, 580, 490, 582, 490, 580, 492, 584, 488, 584, 488, 582, 490, 1660, 492, 1660, 492, 582, 492, 580, 492, 580, 492, 582, 490, 580, 490, 582, 490, 582, 488, 1658, 490, 582, 490, 1660, 490, 582, 490, 584, 490, 1686, 466, 582, 490, 1660, 490, 606, 464, 582, 490, 582, 492, 578, 492, 586, 488, 578, 492, 580, 492, 1684, 466, 608, 466, 1660, 490, 580, 492, 606, 466, 580, 492, 580, 492, 582, 492, 1656, 492, 580, 492, 1660, 490, 1660, 492, 582, 492, 580, 492, 582, 490, 582, 492, 1660, 492, 1660, 490, 580, 492, 582, 490, 582, 492, 582, 492, 1658, 492, 1658, 492, 1660, 492, 1662, 490, 580, 492, 582, 490, 1660, 490, 580, 490 };  // MIRAGE
          Serial.println("Mode 3 Fan");
          irsend.sendRaw(rawData, 243, 38);
          Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC FAN ON", t, " & Cold V: ", cold, "/Hot V: ", hot);
          thand = 1;
          a = 0;
          b = 0;
        }
        return;
      }
    } else if (a == 3) {
      exacttemp = dht.readTemperature() + 0.2;
      a++;
    } else if (a == 4 && b < 16) {
      if (b < 15) {
        b++;
      } else if (b == 15) {
        if (exacttemp > t) {
          b++;
          thand = 0;
        } else if (exacttemp <= t) {
          a = 0;
          b = 0;
          thand = 0;
        }
      }
    } else if (t < cold && thand == 0 && fanmode == 0) {
      uint16_t rawData[243] = { 8282, 4264, 464, 580, 492, 1684, 466, 1684, 466, 606, 464, 1684, 466, 580, 490, 1682, 466, 608, 468, 604, 466, 1656, 494, 606, 464, 606, 464, 1684, 464, 1684, 464, 1660, 492, 582, 490, 580, 492, 582, 490, 606, 464, 580, 492, 580, 492, 606, 464, 580, 490, 608, 466, 578, 492, 578, 492, 582, 490, 584, 486, 606, 466, 580, 490, 580, 492, 582, 490, 1658, 490, 582, 490, 606, 464, 606, 466, 606, 464, 1660, 492, 580, 492, 582, 492, 580, 490, 580, 492, 552, 520, 582, 490, 606, 464, 608, 464, 1656, 494, 1658, 492, 580, 492, 580, 492, 606, 466, 606, 466, 582, 490, 580, 490, 582, 490, 578, 494, 580, 490, 580, 492, 580, 492, 580, 492, 606, 466, 580, 492, 582, 490, 580, 492, 580, 492, 580, 492, 1684, 466, 1658, 492, 582, 492, 580, 490, 580, 492, 606, 466, 606, 464, 580, 492, 580, 490, 1656, 492, 582, 490, 1656, 492, 580, 490, 554, 520, 1656, 492, 580, 492, 1658, 492, 580, 492, 580, 492, 580, 490, 580, 494, 580, 492, 578, 492, 1658, 492, 580, 492, 582, 490, 1686, 466, 1660, 490, 580, 492, 584, 488, 1660, 492, 580, 492, 580, 490, 580, 490, 578, 494, 580, 492, 606, 466, 580, 492, 1658, 492, 580, 492, 1660, 490, 1660, 492, 580, 490, 580, 492, 606, 466, 580, 492, 1658, 490, 580, 492, 580, 490, 580, 492, 1656, 494, 580, 492, 1684, 464, 580, 492 };  // MIRAGE
      irsend.sendRaw(rawData, 243, 38);
      Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC OFF at ", t, " & Cold V: ", cold, "/Hot V: ", hot);
      Serial.println("Turning the Auto - Ac off");
      thand = 1;
      a = 0;
      b = 0;
    } else if (t < cold && thand == 0 && fanmode == 1) {
      uint16_t rawData[243] = { 8304, 4238, 490, 582, 490, 1660, 490, 1686, 464, 608, 464, 1686, 464, 582, 492, 1658, 492, 582, 490, 608, 464, 582, 488, 1662, 490, 584, 488, 1686, 466, 1686, 464, 1688, 464, 582, 488, 582, 490, 608, 464, 582, 490, 608, 464, 610, 462, 582, 492, 606, 464, 608, 464, 608, 464, 608, 464, 608, 464, 580, 492, 584, 486, 582, 490, 582, 490, 582, 492, 1686, 464, 608, 464, 606, 464, 608, 464, 1662, 490, 582, 490, 1660, 490, 608, 462, 608, 464, 608, 464, 608, 464, 580, 490, 608, 464, 584, 486, 608, 464, 582, 490, 582, 490, 582, 490, 580, 492, 580, 490, 582, 490, 580, 490, 584, 488, 582, 490, 608, 464, 580, 492, 554, 518, 582, 490, 580, 490, 582, 490, 580, 492, 584, 488, 584, 488, 582, 490, 1660, 492, 1660, 492, 582, 492, 580, 492, 580, 492, 582, 490, 580, 490, 582, 490, 582, 488, 1658, 490, 582, 490, 1660, 490, 582, 490, 584, 490, 1686, 466, 582, 490, 1660, 490, 606, 464, 582, 490, 582, 492, 578, 492, 586, 488, 578, 492, 580, 492, 1684, 466, 608, 466, 1660, 490, 580, 492, 606, 466, 580, 492, 580, 492, 582, 492, 1656, 492, 580, 492, 1660, 490, 1660, 492, 582, 492, 580, 492, 582, 490, 582, 492, 1660, 492, 1660, 490, 580, 492, 582, 490, 582, 492, 582, 492, 1658, 492, 1658, 492, 1660, 492, 1662, 490, 580, 492, 582, 490, 1660, 490, 580, 490 };  // MIRAGE
      Serial.println("Mode 3 Fan");
      irsend.sendRaw(rawData, 243, 38);
      Blynk.virtualWrite(V6, "TEMP : ", t, "/", h, "- Auto AC - ON : AC FAN ON", t, " & Cold V: ", cold, "/Hot V: ", hot);
      thand = 1;
      a = 0;
      b = 0;
    }
  }
}

//Voids......................................................................................................................................................................................................................................



void setup() {
  Serial.begin(9600);
  irsend.begin();
  BlynkEdgent.begin();
  dht.begin();
  timer.setInterval(1000L, SendSensor);
  timer.setInterval(4000L, AUTOTEMP);
  Blynk.syncVirtual(V7);
}

void loop() {
  BlynkEdgent.run();
  timer.run();
}
